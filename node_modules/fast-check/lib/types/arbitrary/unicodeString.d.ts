import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
import type { StringSharedConstraints } from './_shared/StringSharedConstraints.js';
export type { StringSharedConstraints } from './_shared/StringSharedConstraints.js';
/**
 * For strings of {@link unicode}
 *
 * @param constraints - Constraints to apply when building instances (since 2.4.0)
 *
 * @deprecated Please use ${@link string} with `fc.string({ unit, ...constraints })`, utilizing one of its unit variants instead
 * @remarks Since 0.0.11
 * @public
 */
export declare function unicodeString(constraints?: StringSharedConstraints): Arbitrary<string>;
