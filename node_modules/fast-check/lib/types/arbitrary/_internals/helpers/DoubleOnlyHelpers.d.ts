import type { DoubleConstraints } from '../../double.js';
export declare const maxNonIntegerValue = 4503599627370495.5;
export declare const onlyIntegersAfterThisValue = 4503599627370496;
/**
 * Refine source constraints receive by a double to focus only on non-integer values.
 * @param constraints - Source constraints to be refined
 */
export declare function refineConstraintsForDoubleOnly(constraints: Omit<DoubleConstraints, 'noInteger'>): Required<Omit<DoubleConstraints, 'noInteger'>>;
export declare function doubleOnlyMapper(value: number): number;
export declare function doubleOnlyUnmapper(value: unknown): number;
