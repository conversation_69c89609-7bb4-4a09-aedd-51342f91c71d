import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
import type { StringSharedConstraints } from './_shared/StringSharedConstraints.js';
export type { StringSharedConstraints } from './_shared/StringSharedConstraints.js';
/**
 * For strings of {@link hexa}
 *
 * @param constraints - Constraints to apply when building instances (since 2.4.0)
 *
 * @deprecated Please use ${@link string} with `fc.string({ unit: fc.constantFrom(...'0123456789abcdef'), ...constraints })` instead
 * @remarks Since 0.0.1
 * @public
 */
declare function hexaString(constraints?: StringSharedConstraints): Arbitrary<string>;
export { hexaString };
