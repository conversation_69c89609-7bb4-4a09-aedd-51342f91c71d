import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
/**
 * For signed bigint of n bits
 *
 * Generated values will be between -2^(n-1) (included) and 2^(n-1) (excluded)
 *
 * @param n - Maximal number of bits of the generated bigint
 *
 * @deprecated Please use ${@link bigInt} with `fc.bigInt({ min: -2n**(n-1n), max: 2n**(n-1n)-1n })` instead
 * @remarks Since 1.9.0
 * @public
 */
export declare function bigIntN(n: number): Arbitrary<bigint>;
