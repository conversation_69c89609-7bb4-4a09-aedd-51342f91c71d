import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
/**
 * Type of the value produced by {@link clone}
 * @remarks Since 2.5.0
 * @public
 */
export type CloneValue<T, N extends number, Rest extends T[] = []> = [number] extends [N] ? T[] : Rest['length'] extends N ? Rest : CloneValue<T, N, [T, ...Rest]>;
/**
 * Clone the values generated by `arb` in order to produce fully equal values (might not be equal in terms of === or ==)
 *
 * @param arb - Source arbitrary
 * @param numValues - Number of values to produce
 *
 * @remarks Since 2.5.0
 * @public
 */
declare function clone<T, N extends number>(arb: Arbitrary<T>, numValues: N): Arbitrary<CloneValue<T, N>>;
export { clone };
