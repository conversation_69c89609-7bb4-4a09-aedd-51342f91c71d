import type { Stream } from '../stream/Stream.js';
import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
/**
 * Produce an infinite stream of values
 *
 * WARNING: Requires Object.assign
 *
 * @param arb - Arbitrary used to generate the values
 *
 * @remarks Since 1.8.0
 * @public
 */
declare function infiniteStream<T>(arb: Arbitrary<T>): Arbitrary<Stream<T>>;
export { infiniteStream };
