import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
/**
 * For unsigned bigint of n bits
 *
 * Generated values will be between 0 (included) and 2^n (excluded)
 *
 * @param n - Maximal number of bits of the generated bigint
 *
 * @deprecated Please use ${@link bigInt} with `fc.bigInt({ min: 0n, max: 2n**n-1n })` instead
 * @remarks Since 1.9.0
 * @public
 */
export declare function bigUintN(n: number): Arbitrary<bigint>;
