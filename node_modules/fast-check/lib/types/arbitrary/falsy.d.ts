import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
/**
 * Constraints to be applied on {@link falsy}
 * @remarks Since 1.26.0
 * @public
 */
export interface FalsyContraints {
    /**
     * Enable falsy bigint value
     * @remarks Since 1.26.0
     */
    withBigInt?: boolean;
}
/**
 * Typing for values generated by {@link falsy}
 * @remarks Since 2.2.0
 * @public
 */
export type FalsyValue<TConstraints extends FalsyContraints = {}> = false | null | 0 | '' | typeof NaN | undefined | (TConstraints extends {
    withBigInt: true;
} ? 0n : never);
/**
 * For falsy values:
 * - ''
 * - 0
 * - NaN
 * - false
 * - null
 * - undefined
 * - 0n (whenever withBigInt: true)
 *
 * @param constraints - Constraints to apply when building instances
 *
 * @remarks Since 1.26.0
 * @public
 */
export declare function falsy<TConstraints extends FalsyContraints>(constraints?: TConstraints): Arbitrary<FalsyValue<TConstraints>>;
