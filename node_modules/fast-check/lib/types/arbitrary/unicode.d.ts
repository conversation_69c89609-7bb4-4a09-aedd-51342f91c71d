import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
/**
 * For single unicode characters defined in the BMP plan - char code between 0x0000 (included) and 0xffff (included) and without the range 0xd800 to 0xdfff (surrogate pair characters)
 * @deprecated Please use ${@link string} with `fc.string({ unit, minLength: 1, maxLength: 1 })`, utilizing one of its unit variants instead
 * @remarks Since 0.0.11
 * @public
 */
export declare function unicode(): Arbitrary<string>;
