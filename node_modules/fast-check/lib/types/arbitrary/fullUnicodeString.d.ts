import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
import type { StringSharedConstraints } from './_shared/StringSharedConstraints.js';
export type { StringSharedConstraints } from './_shared/StringSharedConstraints.js';
/**
 * For strings of {@link fullUnicode}
 *
 * @param constraints - Constraints to apply when building instances (since 2.4.0)
 *
 * @deprecated Please use ${@link string} with `fc.string({ unit: 'grapheme', ...constraints })` or `fc.string({ unit: 'binary', ...constraints })` instead
 * @remarks Since 0.0.11
 * @public
 */
export declare function fullUnicodeString(constraints?: StringSharedConstraints): Arbitrary<string>;
