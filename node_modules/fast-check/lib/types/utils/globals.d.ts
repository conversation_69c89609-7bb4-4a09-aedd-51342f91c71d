declare const SArray: typeof Array;
export { SArray as Array };
declare const SBigInt: typeof BigInt;
export { SBigInt as BigInt };
declare const SBigInt64Array: typeof BigInt64Array;
export { SBigInt64Array as BigInt64Array };
declare const SBigUint64Array: typeof BigUint64Array;
export { SBigUint64Array as BigUint64Array };
declare const SBoolean: typeof Boolean;
export { SBoolean as Boolean };
declare const SDate: typeof Date;
export { SDate as Date };
declare const SError: typeof Error;
export { SError as Error };
declare const SFloat32Array: typeof Float32Array;
export { SFloat32Array as Float32Array };
declare const SFloat64Array: typeof Float64Array;
export { SFloat64Array as Float64Array };
declare const SInt8Array: typeof Int8Array;
export { SInt8Array as Int8Array };
declare const SInt16Array: typeof Int16Array;
export { SInt16Array as Int16Array };
declare const SInt32Array: typeof Int32Array;
export { SInt32Array as Int32Array };
declare const SNumber: typeof Number;
export { SNumber as Number };
declare const SString: typeof String;
export { SString as String };
declare const SSet: typeof Set;
export { SSet as Set };
declare const SUint8Array: typeof Uint8Array;
export { SUint8Array as Uint8Array };
declare const SUint8ClampedArray: typeof Uint8ClampedArray;
export { SUint8ClampedArray as Uint8ClampedArray };
declare const SUint16Array: typeof Uint16Array;
export { SUint16Array as Uint16Array };
declare const SUint32Array: typeof Uint32Array;
export { SUint32Array as Uint32Array };
declare const SencodeURIComponent: typeof encodeURIComponent;
export { SencodeURIComponent as encodeURIComponent };
declare const SMap: MapConstructor;
export { SMap as Map };
declare const SSymbol: SymbolConstructor;
export { SSymbol as Symbol };
export declare function safeForEach<T>(instance: T[], fn: (value: T, index: number, array: T[]) => void): void;
export declare function safeIndexOf<T>(instance: readonly T[], ...args: [searchElement: T, fromIndex?: number | undefined]): number;
export declare function safeJoin<T>(instance: T[], ...args: [separator?: string | undefined]): string;
export declare function safeMap<T, U>(instance: T[], fn: (value: T, index: number, array: T[]) => U): U[];
export declare function safeFilter<T, U extends T>(instance: T[], predicate: ((value: T, index: number, array: T[]) => value is U) | ((value: T, index: number, array: T[]) => unknown)): U[];
export declare function safePush<T>(instance: T[], ...args: T[]): number;
export declare function safePop<T>(instance: T[]): T | undefined;
export declare function safeSplice<T>(instance: T[], ...args: [start: number, deleteCount?: number | undefined]): T[];
export declare function safeSlice<T>(instance: T[], ...args: [start?: number | undefined, end?: number | undefined]): T[];
export declare function safeSort<T>(instance: T[], ...args: [compareFn?: ((a: T, b: T) => number) | undefined]): T[];
export declare function safeEvery<T>(instance: T[], ...args: [predicate: (value: T) => boolean]): boolean;
export declare function safeGetTime(instance: Date): number;
export declare function safeToISOString(instance: Date): string;
export declare function safeAdd<T>(instance: Set<T>, value: T): Set<T>;
export declare function safeHas<T>(instance: Set<T>, value: T): boolean;
export declare function safeSet<T extends object, U>(instance: WeakMap<T, U>, key: T, value: U): WeakMap<T, U>;
export declare function safeGet<T extends object, U>(instance: WeakMap<T, U>, key: T): U | undefined;
export declare function safeMapSet<T, U>(instance: Map<T, U>, key: T, value: U): Map<T, U>;
export declare function safeMapGet<T, U>(instance: Map<T, U>, key: T): U | undefined;
export declare function safeSplit(instance: string, ...args: [separator: string | RegExp, limit?: number | undefined]): string[];
export declare function safeStartsWith(instance: string, ...args: [searchString: string, position?: number | undefined]): boolean;
export declare function safeEndsWith(instance: string, ...args: [searchString: string, endPosition?: number | undefined]): boolean;
export declare function safeSubstring(instance: string, ...args: [start: number, end?: number | undefined]): string;
export declare function safeToLowerCase(instance: string): string;
export declare function safeToUpperCase(instance: string): string;
export declare function safePadStart(instance: string, ...args: [maxLength: number, fillString?: string | undefined]): string;
export declare function safeCharCodeAt(instance: string, index: number): number;
export declare function safeNormalize(instance: string, form: 'NFC' | 'NFD' | 'NFKC' | 'NFKD'): string;
export declare function safeReplace(instance: string, pattern: RegExp | string, replacement: string): string;
export declare function safeNumberToString(instance: number, ...args: [radix?: number | undefined]): string;
export declare function safeHasOwnProperty(instance: unknown, v: PropertyKey): boolean;
export declare function safeToString(instance: unknown): string;
