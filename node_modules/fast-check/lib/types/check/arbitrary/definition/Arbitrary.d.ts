import type { Random } from '../../../random/generator/Random.js';
import { Stream } from '../../../stream/Stream.js';
import { Value } from './Value.js';
/**
 * Abstract class able to generate values on type `T`
 *
 * The values generated by an instance of Arbitrary can be previewed - with {@link sample} - or classified - with {@link statistics}.
 *
 * @remarks Since 0.0.7
 * @public
 */
export declare abstract class Arbitrary<T> {
    /**
     * Generate a value of type `T` along with its context (if any)
     * based on the provided random number generator
     *
     * @param mrng - Random number generator
     * @param biasFactor - If taken into account 1 value over biasFactor must be biased. Either integer value greater or equal to 2 (bias) or undefined (no bias)
     * @returns Random value of type `T` and its context
     *
     * @remarks Since 0.0.1 (return type changed in 3.0.0)
     */
    abstract generate(mrng: Random, biasFactor: number | undefined): Value<T>;
    /**
     * Check if a given value could be pass to `shrink` without providing any context.
     *
     * In general, `canShrinkWithoutContext` is not designed to be called for each `shrink` but rather on very special cases.
     * Its usage must be restricted to `canShrinkWithoutContext` or in the rare* contexts of a `shrink` method being called without
     * any context. In this ill-formed case of `shrink`, `canShrinkWithoutContext` could be used or called if needed.
     *
     * *we fall in that case when fast-check is asked to shrink a value that has been provided manually by the user,
     *  in other words: a value not coming from a call to `generate` or a normal `shrink` with context.
     *
     * @param value - Value to be assessed
     * @returns `true` if and only if the value could have been generated by this instance
     *
     * @remarks Since 3.0.0
     */
    abstract canShrinkWithoutContext(value: unknown): value is T;
    /**
     * Shrink a value of type `T`, may rely on the context previously provided to shrink efficiently
     *
     * Must never be called with possibly invalid values and no context without ensuring that such call is legal
     * by calling `canShrinkWithoutContext` first on the value.
     *
     * @param value - The value to shrink
     * @param context - Its associated context (the one returned by generate) or `undefined` if no context but `canShrinkWithoutContext(value) === true`
     * @returns Stream of shrinks for value based on context (if provided)
     *
     * @remarks Since 3.0.0
     */
    abstract shrink(value: T, context: unknown | undefined): Stream<Value<T>>;
    /**
     * Create another arbitrary by filtering values against `predicate`
     *
     * All the values produced by the resulting arbitrary
     * satisfy `predicate(value) == true`
     *
     * Be aware that using filter may highly impact the time required to generate a valid entry
     *
     * @example
     * ```typescript
     * const integerGenerator: Arbitrary<number> = ...;
     * const evenIntegerGenerator: Arbitrary<number> = integerGenerator.filter(e => e % 2 === 0);
     * // new Arbitrary only keeps even values
     * ```
     *
     * @param refinement - Predicate, to test each produced element. Return true to keep the element, false otherwise
     * @returns New arbitrary filtered using predicate
     *
     * @remarks Since 1.23.0
     */
    filter<U extends T>(refinement: (t: T) => t is U): Arbitrary<U>;
    /**
     * Create another arbitrary by filtering values against `predicate`
     *
     * All the values produced by the resulting arbitrary
     * satisfy `predicate(value) == true`
     *
     * Be aware that using filter may highly impact the time required to generate a valid entry
     *
     * @example
     * ```typescript
     * const integerGenerator: Arbitrary<number> = ...;
     * const evenIntegerGenerator: Arbitrary<number> = integerGenerator.filter(e => e % 2 === 0);
     * // new Arbitrary only keeps even values
     * ```
     *
     * @param predicate - Predicate, to test each produced element. Return true to keep the element, false otherwise
     * @returns New arbitrary filtered using predicate
     *
     * @remarks Since 0.0.1
     */
    filter(predicate: (t: T) => boolean): Arbitrary<T>;
    /**
     * Create another arbitrary by mapping all produced values using the provided `mapper`
     * Values produced by the new arbitrary are the result of applying `mapper` value by value
     *
     * @example
     * ```typescript
     * const rgbChannels: Arbitrary<{r:number,g:number,b:number}> = ...;
     * const color: Arbitrary<string> = rgbChannels.map(ch => `#${(ch.r*65536 + ch.g*256 + ch.b).toString(16).padStart(6, '0')}`);
     * // transform an Arbitrary producing {r,g,b} integers into an Arbitrary of '#rrggbb'
     * ```
     *
     * @param mapper - Map function, to produce a new element based on an old one
     * @param unmapper - Optional unmap function, it will never be used except when shrinking user defined values. Must throw if value is not compatible (since 3.0.0)
     * @returns New arbitrary with mapped elements
     *
     * @remarks Since 0.0.1
     */
    map<U>(mapper: (t: T) => U, unmapper?: (possiblyU: unknown) => T): Arbitrary<U>;
    /**
     * Create another arbitrary by mapping a value from a base Arbirary using the provided `fmapper`
     * Values produced by the new arbitrary are the result of the arbitrary generated by applying `fmapper` to a value
     * @example
     * ```typescript
     * const arrayAndLimitArbitrary = fc.nat().chain((c: number) => fc.tuple( fc.array(fc.nat(c)), fc.constant(c)));
     * ```
     *
     * @param chainer - Chain function, to produce a new Arbitrary using a value from another Arbitrary
     * @returns New arbitrary of new type
     *
     * @remarks Since 1.2.0
     */
    chain<U>(chainer: (t: T) => Arbitrary<U>): Arbitrary<U>;
    /**
     * Create another Arbitrary with no shrink values
     *
     * @example
     * ```typescript
     * const dataGenerator: Arbitrary<string> = ...;
     * const unshrinkableDataGenerator: Arbitrary<string> = dataGenerator.noShrink();
     * // same values no shrink
     * ```
     *
     * @returns Create another arbitrary with no shrink values
     * @remarks Since 0.0.9
     */
    noShrink(): Arbitrary<T>;
    /**
     * Create another Arbitrary that cannot be biased
     *
     * @param freq - The biased version will be used one time over freq - if it exists
     * @remarks Since 1.1.0
     */
    noBias(): Arbitrary<T>;
}
