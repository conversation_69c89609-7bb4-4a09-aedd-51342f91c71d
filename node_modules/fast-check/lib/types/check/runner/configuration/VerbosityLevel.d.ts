/**
 * Verbosity level
 * @remarks Since 1.9.1
 * @public
 */
export declare enum VerbosityLevel {
    /**
     * Level 0 (default)
     *
     * Minimal reporting:
     * - minimal failing case
     * - error log corresponding to the minimal failing case
     *
     * @remarks Since 1.9.1
     */
    None = 0,
    /**
     * Level 1
     *
     * Failures reporting:
     * - same as `VerbosityLevel.None`
     * - list all the failures encountered during the shrinking process
     *
     * @remarks Since 1.9.1
     */
    Verbose = 1,
    /**
     * Level 2
     *
     * Execution flow reporting:
     * - same as `VerbosityLevel.None`
     * - all runs with their associated status displayed as a tree
     *
     * @remarks Since 1.9.1
     */
    VeryVerbose = 2
}
