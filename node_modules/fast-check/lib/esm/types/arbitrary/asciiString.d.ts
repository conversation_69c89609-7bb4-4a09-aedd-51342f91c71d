import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
import type { StringSharedConstraints } from './_shared/StringSharedConstraints.js';
export type { StringSharedConstraints } from './_shared/StringSharedConstraints.js';
/**
 * For strings of {@link ascii}
 *
 * @param constraints - Constraints to apply when building instances (since 2.4.0)
 *
 * @deprecated Please use ${@link string} with `fc.string({ unit: 'binary-ascii', ...constraints })` instead
 * @remarks Since 0.0.1
 * @public
 */
export declare function asciiString(constraints?: StringSharedConstraints): Arbitrary<string>;
