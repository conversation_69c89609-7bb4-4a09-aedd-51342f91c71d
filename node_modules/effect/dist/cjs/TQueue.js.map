{"version": 3, "file": "TQueue.js", "names": ["internal", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TDequeueTypeId", "exports", "TEnqueueTypeId", "isTQueue", "isTDequeue", "isTEnqueue", "await<PERSON><PERSON><PERSON>down", "bounded", "capacity", "dropping", "isEmpty", "isFull", "isShutdown", "offer", "offerAll", "peek", "peekOption", "poll", "seek", "shutdown", "size", "sliding", "take", "takeAll", "takeBetween", "takeN", "takeUpTo", "unbounded"], "sources": ["../../src/TQueue.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAoD,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAHpD;;;;AASA;;;;AAIO,MAAMkB,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAkBtB,QAAQ,CAACsB,cAAc;AAQpE;;;;AAIO,MAAME,cAAc,GAAAD,OAAA,CAAAC,cAAA,GAAkBxB,QAAQ,CAACwB,cAAc;AAuJpE;;;;;;AAMO,MAAMC,QAAQ,GAAAF,OAAA,CAAAE,QAAA,GAAyCzB,QAAQ,CAACyB,QAAQ;AAE/E;;;;;;AAMO,MAAMC,UAAU,GAAAH,OAAA,CAAAG,UAAA,GAA2C1B,QAAQ,CAAC0B,UAAU;AAErF;;;;;;AAMO,MAAMC,UAAU,GAAAJ,OAAA,CAAAI,UAAA,GAA2C3B,QAAQ,CAAC2B,UAAU;AAErF;;;;;;;;AAQO,MAAMC,aAAa,GAAAL,OAAA,CAAAK,aAAA,GAA0D5B,QAAQ,CAAC4B,aAAa;AAE1G;;;;;;;;;;AAUO,MAAMC,OAAO,GAAAN,OAAA,CAAAM,OAAA,GAAyD7B,QAAQ,CAAC6B,OAAO;AAE7F;;;;;;AAMO,MAAMC,QAAQ,GAAAP,OAAA,CAAAO,QAAA,GAAmD9B,QAAQ,CAAC8B,QAAQ;AAEzF;;;;;;;;;AASO,MAAMC,QAAQ,GAAAR,OAAA,CAAAQ,QAAA,GAAyD/B,QAAQ,CAAC+B,QAAQ;AAE/F;;;;;;AAMO,MAAMC,OAAO,GAAAT,OAAA,CAAAS,OAAA,GAA6DhC,QAAQ,CAACgC,OAAO;AAEjG;;;;;;;AAOO,MAAMC,MAAM,GAAAV,OAAA,CAAAU,MAAA,GAA6DjC,QAAQ,CAACiC,MAAM;AAE/F;;;;;;AAMO,MAAMC,UAAU,GAAAX,OAAA,CAAAW,UAAA,GAA6DlC,QAAQ,CAACkC,UAAU;AAEvG;;;;;;AAMO,MAAMC,KAAK,GAAAZ,OAAA,CAAAY,KAAA,GAednC,QAAQ,CAACmC,KAAK;AAElB;;;;;;;;;;;;;;;;;;AAkBO,MAAMC,QAAQ,GAAAb,OAAA,CAAAa,QAAA,GAuCjBpC,QAAQ,CAACoC,QAAQ;AAErB;;;;;;;AAOO,MAAMC,IAAI,GAAAd,OAAA,CAAAc,IAAA,GAAyCrC,QAAQ,CAACqC,IAAI;AAEvE;;;;;;;AAOO,MAAMC,UAAU,GAAAf,OAAA,CAAAe,UAAA,GAAwDtC,QAAQ,CAACsC,UAAU;AAElG;;;;;;;AAOO,MAAMC,IAAI,GAAAhB,OAAA,CAAAgB,IAAA,GAAwDvC,QAAQ,CAACuC,IAAI;AAEtF;;;;;;;;AAQO,MAAMC,IAAI,GAAAjB,OAAA,CAAAiB,IAAA,GAmBbxC,QAAQ,CAACwC,IAAI;AAEjB;;;;;;;AAOO,MAAMC,QAAQ,GAAAlB,OAAA,CAAAkB,QAAA,GAA0DzC,QAAQ,CAACyC,QAAQ;AAEhG;;;;;;;;AAQO,MAAMC,IAAI,GAAAnB,OAAA,CAAAmB,IAAA,GAA4D1C,QAAQ,CAAC0C,IAAI;AAE1F;;;;;;;;;AASO,MAAMC,OAAO,GAAApB,OAAA,CAAAoB,OAAA,GAAyD3C,QAAQ,CAAC2C,OAAO;AAE7F;;;;;;;AAOO,MAAMC,IAAI,GAAArB,OAAA,CAAAqB,IAAA,GAAyC5C,QAAQ,CAAC4C,IAAI;AAEvE;;;;;;;AAOO,MAAMC,OAAO,GAAAtB,OAAA,CAAAsB,OAAA,GAAgD7C,QAAQ,CAAC6C,OAAO;AAEpF;;;;;;;;AAQO,MAAMC,WAAW,GAAAvB,OAAA,CAAAuB,WAAA,GAmBpB9C,QAAQ,CAAC8C,WAAW;AAExB;;;;;;;;AAQO,MAAMC,KAAK,GAAAxB,OAAA,CAAAwB,KAAA,GAmBd/C,QAAQ,CAAC+C,KAAK;AAElB;;;;;;AAMO,MAAMC,QAAQ,GAAAzB,OAAA,CAAAyB,QAAA,GAejBhD,QAAQ,CAACgD,QAAQ;AAErB;;;;;;AAMO,MAAMC,SAAS,GAAA1B,OAAA,CAAA0B,SAAA,GAAgCjD,QAAQ,CAACiD,SAAS", "ignoreList": []}