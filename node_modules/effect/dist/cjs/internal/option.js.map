{"version": 3, "file": "option.js", "names": ["Equal", "_interopRequireWildcard", "require", "Hash", "_Inspectable", "_Predicate", "_effectable", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TypeId", "Symbol", "for", "CommonProto", "EffectPrototype", "_A", "_", "NodeInspectSymbol", "toJSON", "toString", "format", "SomeProto", "assign", "create", "_tag", "_op", "symbol", "that", "isOption", "isSome", "equals", "value", "cached", "combine", "hash", "_id", "NoneHash", "NoneProto", "isNone", "input", "hasProperty", "exports", "fa", "none", "some", "a"], "sources": ["../../../src/internal/option.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAIA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,IAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAEA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;AAAiD,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AATjD;;;;AAWA,MAAMkB,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAkB;AAE1E,MAAMC,WAAW,GAAG;EAClB,GAAGC,2BAAe;EAClB,CAACJ,MAAM,GAAG;IACRK,EAAE,EAAGC,CAAQ,IAAKA;GACnB;EACD,CAACC,8BAAiB,IAAC;IACjB,OAAO,IAAI,CAACC,MAAM,EAAE;EACtB,CAAC;EACDC,QAAQA,CAAA;IACN,OAAO,IAAAC,mBAAM,EAAC,IAAI,CAACF,MAAM,EAAE,CAAC;EAC9B;CACD;AAED,MAAMG,SAAS,gBAAGd,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACV,WAAW,CAAC,EAAE;EAC1DW,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,MAAM;EACX,CAACzC,KAAK,CAAC0C,MAAM,EAA2BC,IAAa;IACnD,OAAOC,QAAQ,CAACD,IAAI,CAAC,IAAIE,MAAM,CAACF,IAAI,CAAC,IAAI3C,KAAK,CAAC8C,MAAM,CAAC,IAAI,CAACC,KAAK,EAAEJ,IAAI,CAACI,KAAK,CAAC;EAC/E,CAAC;EACD,CAAC5C,IAAI,CAACuC,MAAM,IAAC;IACX,OAAOvC,IAAI,CAAC6C,MAAM,CAAC,IAAI,EAAE7C,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC+C,IAAI,CAAC,IAAI,CAACV,IAAI,CAAC,CAAC,CAACrC,IAAI,CAAC+C,IAAI,CAAC,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;EACrF,CAAC;EACDb,MAAMA,CAAA;IACJ,OAAO;MACLiB,GAAG,EAAE,QAAQ;MACbX,IAAI,EAAE,IAAI,CAACA,IAAI;MACfO,KAAK,EAAE,IAAAb,mBAAM,EAAC,IAAI,CAACa,KAAK;KACzB;EACH;CACD,CAAC;AAEF,MAAMK,QAAQ,gBAAGjD,IAAI,CAAC+C,IAAI,CAAC,MAAM,CAAC;AAClC,MAAMG,SAAS,gBAAG9B,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACV,WAAW,CAAC,EAAE;EAC1DW,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,MAAM;EACX,CAACzC,KAAK,CAAC0C,MAAM,EAA2BC,IAAa;IACnD,OAAOC,QAAQ,CAACD,IAAI,CAAC,IAAIW,MAAM,CAACX,IAAI,CAAC;EACvC,CAAC;EACD,CAACxC,IAAI,CAACuC,MAAM,IAAC;IACX,OAAOU,QAAQ;EACjB,CAAC;EACDlB,MAAMA,CAAA;IACJ,OAAO;MACLiB,GAAG,EAAE,QAAQ;MACbX,IAAI,EAAE,IAAI,CAACA;KACZ;EACH;CACD,CAAC;AAEF;AACO,MAAMI,QAAQ,GAAIW,KAAc,IAAsC,IAAAC,sBAAW,EAACD,KAAK,EAAE7B,MAAM,CAAC;AAEvG;AAAA+B,OAAA,CAAAb,QAAA,GAAAA,QAAA;AACO,MAAMU,MAAM,GAAOI,EAAoB,IAA2BA,EAAE,CAAClB,IAAI,KAAK,MAAM;AAE3F;AAAAiB,OAAA,CAAAH,MAAA,GAAAA,MAAA;AACO,MAAMT,MAAM,GAAOa,EAAoB,IAA2BA,EAAE,CAAClB,IAAI,KAAK,MAAM;AAE3F;AAAAiB,OAAA,CAAAZ,MAAA,GAAAA,MAAA;AACO,MAAMc,IAAI,GAAAF,OAAA,CAAAE,IAAA,gBAAyBpC,MAAM,CAACgB,MAAM,CAACc,SAAS,CAAC;AAElE;AACO,MAAMO,IAAI,GAAOb,KAAQ,IAAsB;EACpD,MAAMc,CAAC,GAAGtC,MAAM,CAACgB,MAAM,CAACF,SAAS,CAAC;EAClCwB,CAAC,CAACd,KAAK,GAAGA,KAAK;EACf,OAAOc,CAAC;AACV,CAAC;AAAAJ,OAAA,CAAAG,IAAA,GAAAA,IAAA", "ignoreList": []}