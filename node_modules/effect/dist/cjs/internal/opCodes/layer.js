"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.OP_ZIP_WITH = exports.OP_SUSPEND = exports.OP_SCOPED = exports.OP_PROVIDE_MERGE = exports.OP_PROVIDE = exports.OP_FROM_EFFECT = exports.OP_FRESH = exports.OP_FOLD = exports.OP_EXTEND_SCOPE = void 0;
/** @internal */
const OP_EXTEND_SCOPE = exports.OP_EXTEND_SCOPE = "ExtendScope";
/** @internal */
const OP_FOLD = exports.OP_FOLD = "Fold";
/** @internal */
const OP_FRESH = exports.OP_FRESH = "Fresh";
/** @internal */
const OP_FROM_EFFECT = exports.OP_FROM_EFFECT = "FromEffect";
/** @internal */
const OP_SCOPED = exports.OP_SCOPED = "Scoped";
/** @internal */
const OP_SUSPEND = exports.OP_SUSPEND = "Suspend";
/** @internal */
const OP_PROVIDE = exports.OP_PROVIDE = "Provide";
/** @internal */
const OP_PROVIDE_MERGE = exports.OP_PROVIDE_MERGE = "ProvideMerge";
/** @internal */
const OP_ZIP_WITH = exports.OP_ZIP_WITH = "ZipWith";
//# sourceMappingURL=layer.js.map