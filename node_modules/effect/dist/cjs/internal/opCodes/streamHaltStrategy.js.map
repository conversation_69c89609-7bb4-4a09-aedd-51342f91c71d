{"version": 3, "file": "streamHaltStrategy.js", "names": ["OP_LEFT", "exports", "OP_RIGHT", "OP_BOTH", "OP_EITHER"], "sources": ["../../../../src/internal/opCodes/streamHaltStrategy.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA;AACO,MAAMA,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAG,MAAe;AAKtC;AACO,MAAME,QAAQ,GAAAD,OAAA,CAAAC,QAAA,GAAG,OAAgB;AAKxC;AACO,MAAMC,OAAO,GAAAF,OAAA,CAAAE,OAAA,GAAG,MAAe;AAKtC;AACO,MAAMC,SAAS,GAAAH,OAAA,CAAAG,SAAA,GAAG,QAAiB", "ignoreList": []}