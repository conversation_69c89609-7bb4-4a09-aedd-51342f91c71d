{"version": 3, "file": "layer.js", "names": ["OP_EXTEND_SCOPE", "exports", "OP_FOLD", "OP_FRESH", "OP_FROM_EFFECT", "OP_SCOPED", "OP_SUSPEND", "OP_PROVIDE", "OP_PROVIDE_MERGE", "OP_ZIP_WITH"], "sources": ["../../../../src/internal/opCodes/layer.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA;AACO,MAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,aAAsB;AAKrD;AACO,MAAME,OAAO,GAAAD,OAAA,CAAAC,OAAA,GAAG,MAAe;AAKtC;AACO,MAAMC,QAAQ,GAAAF,OAAA,CAAAE,QAAA,GAAG,OAAgB;AAKxC;AACO,MAAMC,cAAc,GAAAH,OAAA,CAAAG,cAAA,GAAG,YAAqB;AAKnD;AACO,MAAMC,SAAS,GAAAJ,OAAA,CAAAI,SAAA,GAAG,QAAiB;AAK1C;AACO,MAAMC,UAAU,GAAAL,OAAA,CAAAK,UAAA,GAAG,SAAkB;AAK5C;AACO,MAAMC,UAAU,GAAAN,OAAA,CAAAM,UAAA,GAAG,SAAkB;AAK5C;AACO,MAAMC,gBAAgB,GAAAP,OAAA,CAAAO,gBAAA,GAAG,cAAuB;AAKvD;AACO,MAAMC,WAAW,GAAAR,OAAA,CAAAQ,WAAA,GAAG,SAAkB", "ignoreList": []}