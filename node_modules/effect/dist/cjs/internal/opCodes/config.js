"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.OP_ZIP_WITH = exports.OP_SEQUENCE = exports.OP_PRIMITIVE = exports.OP_NESTED = exports.OP_MAP_OR_FAIL = exports.OP_LAZY = exports.OP_HASHMAP = exports.OP_FALLBACK = exports.OP_FAIL = exports.OP_DESCRIBED = exports.OP_CONSTANT = void 0;
/** @internal */
const OP_CONSTANT = exports.OP_CONSTANT = "Constant";
/** @internal */
const OP_FAIL = exports.OP_FAIL = "Fail";
/** @internal */
const OP_FALLBACK = exports.OP_FALLBACK = "Fallback";
/** @internal */
const OP_DESCRIBED = exports.OP_DESCRIBED = "Described";
/** @internal */
const OP_LAZY = exports.OP_LAZY = "Lazy";
/** @internal */
const OP_MAP_OR_FAIL = exports.OP_MAP_OR_FAIL = "MapOrFail";
/** @internal */
const OP_NESTED = exports.OP_NESTED = "Nested";
/** @internal */
const OP_PRIMITIVE = exports.OP_PRIMITIVE = "Primitive";
/** @internal */
const OP_SEQUENCE = exports.OP_SEQUENCE = "Sequence";
/** @internal */
const OP_HASHMAP = exports.OP_HASHMAP = "HashMap";
/** @internal */
const OP_ZIP_WITH = exports.OP_ZIP_WITH = "ZipWith";
//# sourceMappingURL=config.js.map