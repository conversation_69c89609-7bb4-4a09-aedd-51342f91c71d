"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.OP_YIELD = exports.OP_WITH_RUNTIME = exports.OP_WHILE = exports.OP_UPDATE_RUNTIME_FLAGS = exports.OP_TAG = exports.OP_SYNC = exports.OP_SUCCESS = exports.OP_REVERT_FLAGS = exports.OP_ON_SUCCESS_AND_FAILURE = exports.OP_ON_SUCCESS = exports.OP_ON_FAILURE = exports.OP_ITERATOR = exports.OP_FAILURE = exports.OP_COMMIT = exports.OP_ASYNC = void 0;
/** @internal */
const OP_ASYNC = exports.OP_ASYNC = "Async";
/** @internal */
const OP_COMMIT = exports.OP_COMMIT = "Commit";
/** @internal */
const OP_FAILURE = exports.OP_FAILURE = "Failure";
/** @internal */
const OP_ON_FAILURE = exports.OP_ON_FAILURE = "OnFailure";
/** @internal */
const OP_ON_SUCCESS = exports.OP_ON_SUCCESS = "OnSuccess";
/** @internal */
const OP_ON_SUCCESS_AND_FAILURE = exports.OP_ON_SUCCESS_AND_FAILURE = "OnSuccessAndFailure";
/** @internal */
const OP_SUCCESS = exports.OP_SUCCESS = "Success";
/** @internal */
const OP_SYNC = exports.OP_SYNC = "Sync";
/** @internal */
const OP_TAG = exports.OP_TAG = "Tag";
/** @internal */
const OP_UPDATE_RUNTIME_FLAGS = exports.OP_UPDATE_RUNTIME_FLAGS = "UpdateRuntimeFlags";
/** @internal */
const OP_WHILE = exports.OP_WHILE = "While";
/** @internal */
const OP_ITERATOR = exports.OP_ITERATOR = "Iterator";
/** @internal */
const OP_WITH_RUNTIME = exports.OP_WITH_RUNTIME = "WithRuntime";
/** @internal */
const OP_YIELD = exports.OP_YIELD = "Yield";
/** @internal */
const OP_REVERT_FLAGS = exports.OP_REVERT_FLAGS = "RevertFlags";
//# sourceMappingURL=effect.js.map