{"version": 3, "file": "configError.js", "names": ["OP_AND", "exports", "OP_OR", "OP_INVALID_DATA", "OP_MISSING_DATA", "OP_SOURCE_UNAVAILABLE", "OP_UNSUPPORTED"], "sources": ["../../../../src/internal/opCodes/configError.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA;AACO,MAAMA,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAG,KAAc;AAKpC;AACO,MAAME,KAAK,GAAAD,OAAA,CAAAC,KAAA,GAAG,IAAa;AAKlC;AACO,MAAMC,eAAe,GAAAF,OAAA,CAAAE,eAAA,GAAG,aAAsB;AAKrD;AACO,MAAMC,eAAe,GAAAH,OAAA,CAAAG,eAAA,GAAG,aAAsB;AAKrD;AACO,MAAMC,qBAAqB,GAAAJ,OAAA,CAAAI,qBAAA,GAAG,mBAA4B;AAKjE;AACO,MAAMC,cAAc,GAAAL,OAAA,CAAAK,cAAA,GAAG,aAAsB", "ignoreList": []}