{"version": 3, "file": "config.js", "names": ["OP_CONSTANT", "exports", "OP_FAIL", "OP_FALLBACK", "OP_DESCRIBED", "OP_LAZY", "OP_MAP_OR_FAIL", "OP_NESTED", "OP_PRIMITIVE", "OP_SEQUENCE", "OP_HASHMAP", "OP_ZIP_WITH"], "sources": ["../../../../src/internal/opCodes/config.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA;AACO,MAAMA,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAG,UAAmB;AAK9C;AACO,MAAME,OAAO,GAAAD,OAAA,CAAAC,OAAA,GAAG,MAAe;AAKtC;AACO,MAAMC,WAAW,GAAAF,OAAA,CAAAE,WAAA,GAAG,UAAmB;AAK9C;AACO,MAAMC,YAAY,GAAAH,OAAA,CAAAG,YAAA,GAAG,WAAoB;AAKhD;AACO,MAAMC,OAAO,GAAAJ,OAAA,CAAAI,OAAA,GAAG,MAAe;AAKtC;AACO,MAAMC,cAAc,GAAAL,OAAA,CAAAK,cAAA,GAAG,WAAoB;AAKlD;AACO,MAAMC,SAAS,GAAAN,OAAA,CAAAM,SAAA,GAAG,QAAiB;AAK1C;AACO,MAAMC,YAAY,GAAAP,OAAA,CAAAO,YAAA,GAAG,WAAoB;AAKhD;AACO,MAAMC,WAAW,GAAAR,OAAA,CAAAQ,WAAA,GAAG,UAAmB;AAK9C;AACO,MAAMC,UAAU,GAAAT,OAAA,CAAAS,UAAA,GAAG,SAAkB;AAK5C;AACO,MAAMC,WAAW,GAAAV,OAAA,CAAAU,WAAA,GAAG,SAAkB", "ignoreList": []}