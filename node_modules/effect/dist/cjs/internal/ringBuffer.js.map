{"version": 3, "file": "ringBuffer.js", "names": ["Chunk", "_interopRequireWildcard", "require", "_Function", "Option", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "capacity", "array", "size", "current", "constructor", "Array", "from", "length", "constUndefined", "head", "fromNullable", "last<PERSON>r<PERSON><PERSON>", "undefined", "index", "put", "value", "increment", "dropLast", "decrement", "toChunk", "begin", "newArray", "slice", "fromIterable"], "sources": ["../../../src/internal/ringBuffer.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAsC,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEtC;AACM,MAAOkB,UAAU;EAKOC,QAAA;EAJpBC,KAAK;EACLC,IAAI,GAAG,CAAC;EACRC,OAAO,GAAG,CAAC;EAEnBC,YAA4BJ,QAAgB;IAAhB,KAAAA,QAAQ,GAARA,QAAQ;IAClC,IAAI,CAACC,KAAK,GAAGI,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEP;IAAQ,CAAE,EAAEQ,wBAAc,CAAC;EAC/D;EAEAC,IAAIA,CAAA;IACF,OAAO9B,MAAM,CAAC+B,YAAY,CAAC,IAAI,CAACT,KAAK,CAAC,IAAI,CAACE,OAAO,CAAC,CAAC;EACtD;EAEAQ,UAAUA,CAAA;IACR,IAAI,IAAI,CAACT,IAAI,KAAK,CAAC,EAAE;MACnB,OAAOU,SAAS;IAClB;IAEA,MAAMC,KAAK,GAAG,IAAI,CAACV,OAAO,KAAK,CAAC,GAAG,IAAI,CAACF,KAAK,CAACM,MAAM,GAAG,CAAC,GAAG,IAAI,CAACJ,OAAO,GAAG,CAAC;IAE3E,OAAO,IAAI,CAACF,KAAK,CAACY,KAAK,CAAC,IAAID,SAAS;EACvC;EAEAE,GAAGA,CAACC,KAAQ;IACV,IAAI,CAACd,KAAK,CAAC,IAAI,CAACE,OAAO,CAAC,GAAGY,KAAK;IAChC,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,IAAI,GAAG,CAAC,EAAE;MACjB,IAAI,CAACgB,SAAS,EAAE;MAChB,IAAI,CAACjB,KAAK,CAAC,IAAI,CAACE,OAAO,CAAC,GAAGS,SAAS;IACtC;EACF;EAEAO,OAAOA,CAAA;IACL,MAAMC,KAAK,GAAG,IAAI,CAACjB,OAAO,GAAG,IAAI,CAACD,IAAI;IACtC,MAAMmB,QAAQ,GAAGD,KAAK,GAAG,CAAC,GACtB,CACA,GAAG,IAAI,CAACnB,KAAK,CAACqB,KAAK,CAAC,IAAI,CAACtB,QAAQ,GAAGoB,KAAK,EAAE,IAAI,CAACpB,QAAQ,CAAC,EACzD,GAAG,IAAI,CAACC,KAAK,CAACqB,KAAK,CAAC,CAAC,EAAE,IAAI,CAACnB,OAAO,CAAC,CACrC,GACC,IAAI,CAACF,KAAK,CAACqB,KAAK,CAACF,KAAK,EAAE,IAAI,CAACjB,OAAO,CAAC;IAEzC,OAAO5B,KAAK,CAACgD,YAAY,CAACF,QAAQ,CAAmB;EACvD;EAEQL,SAASA,CAAA;IACf,IAAI,IAAI,CAACd,IAAI,GAAG,IAAI,CAACF,QAAQ,EAAE;MAC7B,IAAI,CAACE,IAAI,IAAI,CAAC;IAChB;IACA,IAAI,CAACC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO,GAAG,CAAC,IAAI,IAAI,CAACH,QAAQ;EACnD;EAEQkB,SAASA,CAAA;IACf,IAAI,CAAChB,IAAI,IAAI,CAAC;IACd,IAAI,IAAI,CAACC,OAAO,GAAG,CAAC,EAAE;MACpB,IAAI,CAACA,OAAO,IAAI,CAAC;IACnB,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,GAAG,IAAI,CAACH,QAAQ,GAAG,CAAC;IAClC;EACF", "ignoreList": []}