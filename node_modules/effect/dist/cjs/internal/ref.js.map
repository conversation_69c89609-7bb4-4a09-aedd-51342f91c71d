{"version": 3, "file": "ref.js", "names": ["Effectable", "_interopRequireWildcard", "require", "_Function", "MutableRef", "Option", "Readable", "core", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "RefTypeId", "exports", "Symbol", "for", "refVariance", "_A", "_", "RefImpl", "Class", "ref", "commit", "TypeId", "constructor", "sync", "modify", "current", "b", "a", "unsafeMake", "value", "make", "self", "dual", "getAndSet", "getAndUpdate", "getAndUpdateSome", "pf", "option", "_tag", "setAndGet", "modifySome", "fallback", "update", "updateAndGet", "result", "updateSome", "match", "onNone", "onSome", "updateSomeAndGet", "unsafeGet"], "sources": ["../../../src/internal/ref.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,UAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,QAAA,GAAAL,uBAAA,CAAAC,OAAA;AAEA,IAAAK,IAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAiC,SAAAD,wBAAAO,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAO,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEjC;AACO,MAAMkB,SAAS,GAAAC,OAAA,CAAAD,SAAA,gBAAkBE,MAAM,CAACC,GAAG,CAAC,YAAY,CAAkB;AAEjF;AACO,MAAMC,WAAW,GAAAH,OAAA,CAAAG,WAAA,GAAG;EACzB;EACAC,EAAE,EAAGC,CAAM,IAAKA;CACjB;AAED,MAAMC,OAAkB,SAAQlC,UAAU,CAACmC,KAAQ;EAM5BC,GAAA;EALrBC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACjB,GAAG;EACjB;EACS,CAACO,SAAS,IAAII,WAAW;EACzB,CAACzB,QAAQ,CAACgC,MAAM,IAAqBhC,QAAQ,CAACgC,MAAM;EAC7DC,YAAqBH,GAA6B;IAChD,KAAK,EAAE;IADY,KAAAA,GAAG,GAAHA,GAAG;IAEtB,IAAI,CAAChB,GAAG,GAAGb,IAAI,CAACiC,IAAI,CAAC,MAAMpC,UAAU,CAACgB,GAAG,CAAC,IAAI,CAACgB,GAAG,CAAC,CAAC;EACtD;EACShB,GAAG;EACZqB,MAAMA,CAAIzB,CAA4B;IACpC,OAAOT,IAAI,CAACiC,IAAI,CAAC,MAAK;MACpB,MAAME,OAAO,GAAGtC,UAAU,CAACgB,GAAG,CAAC,IAAI,CAACgB,GAAG,CAAC;MACxC,MAAM,CAACO,CAAC,EAAEC,CAAC,CAAC,GAAG5B,CAAC,CAAC0B,OAAO,CAAC;MACzB,IAAKA,OAAmB,KAAME,CAAa,EAAE;QAC3CxC,UAAU,CAACiB,GAAG,CAACuB,CAAC,CAAC,CAAC,IAAI,CAACR,GAAG,CAAC;MAC7B;MACA,OAAOO,CAAC;IACV,CAAC,CAAC;EACJ;;AAGF;AACO,MAAME,UAAU,GAAOC,KAAQ,IAAiB,IAAIZ,OAAO,CAAC9B,UAAU,CAAC2C,IAAI,CAACD,KAAK,CAAC,CAAC;AAE1F;AAAAlB,OAAA,CAAAiB,UAAA,GAAAA,UAAA;AACO,MAAME,IAAI,GAAOD,KAAQ,IAAgCvC,IAAI,CAACiC,IAAI,CAAC,MAAMK,UAAU,CAACC,KAAK,CAAC,CAAC;AAElG;AAAAlB,OAAA,CAAAmB,IAAA,GAAAA,IAAA;AACO,MAAM3B,GAAG,GAAO4B,IAAgB,IAAKA,IAAI,CAAC5B,GAAG;AAEpD;AAAAQ,OAAA,CAAAR,GAAA,GAAAA,GAAA;AACO,MAAMC,GAAG,GAAAO,OAAA,CAAAP,GAAA,gBAAG,IAAA4B,cAAI,EAGrB,CAAC,EAAE,CAAID,IAAgB,EAAEF,KAAQ,KAAKE,IAAI,CAACP,MAAM,CAAC,MAAiB,CAAC,KAAK,CAAC,EAAEK,KAAK,CAAC,CAAC,CAAC;AAEtF;AACO,MAAMI,SAAS,GAAAtB,OAAA,CAAAsB,SAAA,gBAAG,IAAAD,cAAI,EAG3B,CAAC,EAAE,CAAID,IAAgB,EAAEF,KAAQ,KAAKE,IAAI,CAACP,MAAM,CAAEG,CAAC,IAAa,CAACA,CAAC,EAAEE,KAAK,CAAC,CAAC,CAAC;AAE/E;AACO,MAAMK,YAAY,GAAAvB,OAAA,CAAAuB,YAAA,gBAAG,IAAAF,cAAI,EAG9B,CAAC,EAAE,CAAID,IAAgB,EAAEhC,CAAc,KAAKgC,IAAI,CAACP,MAAM,CAAEG,CAAC,IAAa,CAACA,CAAC,EAAE5B,CAAC,CAAC4B,CAAC,CAAC,CAAC,CAAC,CAAC;AAEpF;AACO,MAAMQ,gBAAgB,GAAAxB,OAAA,CAAAwB,gBAAA,gBAAG,IAAAH,cAAI,EAGlC,CAAC,EAAE,CAAID,IAAgB,EAAEK,EAA8B,KACvDL,IAAI,CAACP,MAAM,CAAEK,KAAK,IAAY;EAC5B,MAAMQ,MAAM,GAAGD,EAAE,CAACP,KAAK,CAAC;EACxB,QAAQQ,MAAM,CAACC,IAAI;IACjB,KAAK,MAAM;MAAE;QACX,OAAO,CAACT,KAAK,EAAEA,KAAK,CAAC;MACvB;IACA,KAAK,MAAM;MAAE;QACX,OAAO,CAACA,KAAK,EAAEQ,MAAM,CAACR,KAAK,CAAC;MAC9B;EACF;AACF,CAAC,CAAC,CAAC;AAEL;AACO,MAAMU,SAAS,GAAA5B,OAAA,CAAA4B,SAAA,gBAAG,IAAAP,cAAI,EAG3B,CAAC,EAAE,CAAID,IAAgB,EAAEF,KAAQ,KAAKE,IAAI,CAACP,MAAM,CAAC,MAAc,CAACK,KAAK,EAAEA,KAAK,CAAC,CAAC,CAAC;AAElF;AACO,MAAML,MAAM,GAAAb,OAAA,CAAAa,MAAA,gBAAG,IAAAQ,cAAI,EAGxB,CAAC,EAAE,CAACD,IAAI,EAAEhC,CAAC,KAAKgC,IAAI,CAACP,MAAM,CAACzB,CAAC,CAAC,CAAC;AAEjC;AACO,MAAMyC,UAAU,GAAA7B,OAAA,CAAA6B,UAAA,gBAAG,IAAAR,cAAI,EAU5B,CAAC,EAAE,CAACD,IAAI,EAAEU,QAAQ,EAAEL,EAAE,KACtBL,IAAI,CAACP,MAAM,CAAEK,KAAK,IAAI;EACpB,MAAMQ,MAAM,GAAGD,EAAE,CAACP,KAAK,CAAC;EACxB,QAAQQ,MAAM,CAACC,IAAI;IACjB,KAAK,MAAM;MAAE;QACX,OAAO,CAACG,QAAQ,EAAEZ,KAAK,CAAC;MAC1B;IACA,KAAK,MAAM;MAAE;QACX,OAAOQ,MAAM,CAACR,KAAK;MACrB;EACF;AACF,CAAC,CAAC,CAAC;AAEL;AACO,MAAMa,MAAM,GAAA/B,OAAA,CAAA+B,MAAA,gBAAG,IAAAV,cAAI,EAGxB,CAAC,EAAE,CAAID,IAAgB,EAAEhC,CAAc,KAAKgC,IAAI,CAACP,MAAM,CAAEG,CAAC,IAAgB,CAAC,KAAK,CAAC,EAAE5B,CAAC,CAAC4B,CAAC,CAAC,CAAC,CAAC,CAAC;AAE5F;AACO,MAAMgB,YAAY,GAAAhC,OAAA,CAAAgC,YAAA,gBAAG,IAAAX,cAAI,EAG9B,CAAC,EAAE,CAAID,IAAgB,EAAEhC,CAAc,KACvCgC,IAAI,CAACP,MAAM,CAAEG,CAAC,IAAY;EACxB,MAAMiB,MAAM,GAAG7C,CAAC,CAAC4B,CAAC,CAAC;EACnB,OAAO,CAACiB,MAAM,EAAEA,MAAM,CAAC;AACzB,CAAC,CAAC,CAAC;AAEL;AACO,MAAMC,UAAU,GAAAlC,OAAA,CAAAkC,UAAA,gBAAG,IAAAb,cAAI,EAG5B,CAAC,EAAE,CAAID,IAAgB,EAAEhC,CAA6B,KACtDgC,IAAI,CAACP,MAAM,CACRG,CAAC,IAAgB,CAChB,KAAK,CAAC,EACNvC,MAAM,CAAC0D,KAAK,CAAC/C,CAAC,CAAC4B,CAAC,CAAC,EAAE;EACjBoB,MAAM,EAAEA,CAAA,KAAMpB,CAAC;EACfqB,MAAM,EAAGtB,CAAC,IAAKA;CAChB,CAAC,CACH,CACF,CAAC;AAEJ;AACO,MAAMuB,gBAAgB,GAAAtC,OAAA,CAAAsC,gBAAA,gBAAG,IAAAjB,cAAI,EAGlC,CAAC,EAAE,CAAID,IAAgB,EAAEK,EAA8B,KACvDL,IAAI,CAACP,MAAM,CAAEK,KAAK,IAAY;EAC5B,MAAMQ,MAAM,GAAGD,EAAE,CAACP,KAAK,CAAC;EACxB,QAAQQ,MAAM,CAACC,IAAI;IACjB,KAAK,MAAM;MAAE;QACX,OAAO,CAACT,KAAK,EAAEA,KAAK,CAAC;MACvB;IACA,KAAK,MAAM;MAAE;QACX,OAAO,CAACQ,MAAM,CAACR,KAAK,EAAEQ,MAAM,CAACR,KAAK,CAAC;MACrC;EACF;AACF,CAAC,CAAC,CAAC;AAEL;AACO,MAAMqB,SAAS,GAAOnB,IAAgB,IAAQ5C,UAAU,CAACgB,GAAG,CAAE4B,IAAmB,CAACZ,GAAG,CAAC;AAAAR,OAAA,CAAAuC,SAAA,GAAAA,SAAA", "ignoreList": []}