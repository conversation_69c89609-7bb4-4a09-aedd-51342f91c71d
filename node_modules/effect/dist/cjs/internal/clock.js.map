{"version": 3, "file": "clock.js", "names": ["Context", "_interopRequireWildcard", "require", "Duration", "_Function", "core", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ClockSymbolKey", "ClockTypeId", "exports", "Symbol", "for", "clockTag", "GenericTag", "MAX_TIMER_MILLIS", "globalClockScheduler", "unsafeSchedule", "task", "duration", "millis", "<PERSON><PERSON><PERSON><PERSON>", "constFalse", "completed", "handle", "setTimeout", "clearTimeout", "performanceNowNanos", "bigint1e6", "BigInt", "performance", "Date", "now", "origin", "undefined", "Math", "round", "processOrPerformanceNow", "processHrtime", "process", "hrtime", "bigint", "ClockImpl", "unsafeCurrentTimeMillis", "unsafeCurrentTimeNanos", "currentTimeMillis", "sync", "currentTimeNanos", "scheduler", "succeed", "sleep", "async", "resume", "canceler", "void", "asVoid", "make"], "sources": ["../../../src/internal/clock.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AAEA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAiC,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEjC;AACA,MAAMkB,cAAc,GAAG,cAAc;AAErC;AACO,MAAMC,WAAW,GAAAC,OAAA,CAAAD,WAAA,gBAAsBE,MAAM,CAACC,GAAG,CAACJ,cAAc,CAAsB;AAE7F;AACO,MAAMK,QAAQ,GAAAH,OAAA,CAAAG,QAAA,gBAA0C9B,OAAO,CAAC+B,UAAU,CAAC,cAAc,CAAC;AAEjG;AACO,MAAMC,gBAAgB,GAAAL,OAAA,CAAAK,gBAAA,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AAE3C;AACO,MAAMC,oBAAoB,GAAAN,OAAA,CAAAM,oBAAA,GAAyB;EACxDC,cAAcA,CAACC,IAAgB,EAAEC,QAA2B;IAC1D,MAAMC,MAAM,GAAGlC,QAAQ,CAACmC,QAAQ,CAACF,QAAQ,CAAC;IAC1C;IACA;IACA,IAAIC,MAAM,GAAGL,gBAAgB,EAAE;MAC7B,OAAOO,oBAAU;IACnB;IACA,IAAIC,SAAS,GAAG,KAAK;IACrB,MAAMC,MAAM,GAAGC,UAAU,CAAC,MAAK;MAC7BF,SAAS,GAAG,IAAI;MAChBL,IAAI,EAAE;IACR,CAAC,EAAEE,MAAM,CAAC;IACV,OAAO,MAAK;MACVM,YAAY,CAACF,MAAM,CAAC;MACpB,OAAO,CAACD,SAAS;IACnB,CAAC;EACH;CACD;AAED,MAAMI,mBAAmB,gBAAI;EAC3B,MAAMC,SAAS,gBAAGC,MAAM,CAAC,SAAS,CAAC;EACnC,IAAI,OAAOC,WAAW,KAAK,WAAW,EAAE;IACtC,OAAO,MAAMD,MAAM,CAACE,IAAI,CAACC,GAAG,EAAE,CAAC,GAAGJ,SAAS;EAC7C;EACA,IAAIK,MAAc;EAClB,OAAO,MAAK;IACV,IAAIA,MAAM,KAAKC,SAAS,EAAE;MACxBD,MAAM,GAAIJ,MAAM,CAACE,IAAI,CAACC,GAAG,EAAE,CAAC,GAAGJ,SAAS,GAAIC,MAAM,CAACM,IAAI,CAACC,KAAK,CAACN,WAAW,CAACE,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;IAC/F;IACA,OAAOC,MAAM,GAAGJ,MAAM,CAACM,IAAI,CAACC,KAAK,CAACN,WAAW,CAACE,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC;EACjE,CAAC;AACH,CAAC,CAAC,CAAE;AACJ,MAAMK,uBAAuB,gBAAI;EAC/B,MAAMC,aAAa,GACjB,OAAOC,OAAO,KAAK,QAAQ,IAAI,QAAQ,IAAIA,OAAO,IAAI,OAAOA,OAAO,CAACC,MAAM,CAACC,MAAM,KAAK,UAAU,GAC/FF,OAAO,CAACC,MAAM,GACdN,SAAS;EACb,IAAI,CAACI,aAAa,EAAE;IAClB,OAAOX,mBAAmB;EAC5B;EACA,MAAMM,MAAM,GAAG,aAAAN,mBAAmB,EAAE,gBAAGW,aAAa,CAACG,MAAM,EAAE;EAC7D,OAAO,MAAMR,MAAM,GAAGK,aAAa,CAACG,MAAM,EAAE;AAC9C,CAAC,CAAC,CAAE;AAEJ;AACA,MAAMC,SAAS;EACJ,CAACjC,WAAW,IAAuBA,WAAW;EAEvDkC,uBAAuBA,CAAA;IACrB,OAAOZ,IAAI,CAACC,GAAG,EAAE;EACnB;EAEAY,sBAAsBA,CAAA;IACpB,OAAOP,uBAAuB,EAAE;EAClC;EAEAQ,iBAAiB,gBAA0BzD,IAAI,CAAC0D,IAAI,CAAC,MAAM,IAAI,CAACH,uBAAuB,EAAE,CAAC;EAE1FI,gBAAgB,gBAA0B3D,IAAI,CAAC0D,IAAI,CAAC,MAAM,IAAI,CAACF,sBAAsB,EAAE,CAAC;EAExFI,SAASA,CAAA;IACP,OAAO5D,IAAI,CAAC6D,OAAO,CAACjC,oBAAoB,CAAC;EAC3C;EAEAkC,KAAKA,CAAC/B,QAA2B;IAC/B,OAAO/B,IAAI,CAAC+D,KAAK,CAAQC,MAAM,IAAI;MACjC,MAAMC,QAAQ,GAAGrC,oBAAoB,CAACC,cAAc,CAAC,MAAMmC,MAAM,CAAChE,IAAI,CAACkE,IAAI,CAAC,EAAEnC,QAAQ,CAAC;MACvF,OAAO/B,IAAI,CAACmE,MAAM,CAACnE,IAAI,CAAC0D,IAAI,CAACO,QAAQ,CAAC,CAAC;IACzC,CAAC,CAAC;EACJ;;AAGF;AACO,MAAMG,IAAI,GAAGA,CAAA,KAAmB,IAAId,SAAS,EAAE;AAAAhC,OAAA,CAAA8C,IAAA,GAAAA,IAAA", "ignoreList": []}