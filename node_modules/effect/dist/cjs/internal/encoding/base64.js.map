{"version": 3, "file": "base64.js", "names": ["Either", "_interopRequireWildcard", "require", "_common", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "encode", "bytes", "length", "result", "base64abc", "exports", "decode", "str", "stripped", "stripCrlf", "left", "DecodeException", "index", "indexOf", "missingOctets", "endsWith", "Uint8Array", "j", "buffer", "getBase64Code", "charCodeAt", "right", "Error", "message", "replace", "charCode", "base64codes", "TypeError", "String", "fromCharCode", "code"], "sources": ["../../../../src/internal/encoding/base64.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAA6C,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE7C;AACO,MAAMkB,MAAM,GAAIC,KAAiB,IAAI;EAC1C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;EAE3B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIf,CAAS;EAEb,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,MAAM,EAAEd,CAAC,IAAI,CAAC,EAAE;IAC9Be,MAAM,IAAIC,SAAS,CAACH,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACtCe,MAAM,IAAIC,SAAS,CAAE,CAACH,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAKa,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,CAAC;IACvEe,MAAM,IAAIC,SAAS,CAAE,CAACH,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAKa,KAAK,CAACb,CAAC,CAAC,IAAI,CAAE,CAAC;IACnEe,MAAM,IAAIC,SAAS,CAACH,KAAK,CAACb,CAAC,CAAC,GAAG,IAAI,CAAC;EACtC;EAEA,IAAIA,CAAC,KAAKc,MAAM,GAAG,CAAC,EAAE;IACpB;IACAC,MAAM,IAAIC,SAAS,CAACH,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACtCe,MAAM,IAAIC,SAAS,CAAC,CAACH,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;IAC/Ce,MAAM,IAAI,IAAI;EAChB;EAEA,IAAIf,CAAC,KAAKc,MAAM,EAAE;IAChB;IACAC,MAAM,IAAIC,SAAS,CAACH,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACtCe,MAAM,IAAIC,SAAS,CAAE,CAACH,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAKa,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,CAAC;IACvEe,MAAM,IAAIC,SAAS,CAAC,CAACH,KAAK,CAACb,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;IAC/Ce,MAAM,IAAI,GAAG;EACf;EAEA,OAAOA,MAAM;AACf,CAAC;AAED;AAAAE,OAAA,CAAAL,MAAA,GAAAA,MAAA;AACO,MAAMM,MAAM,GAAIC,GAAW,IAAyD;EACzF,MAAMC,QAAQ,GAAGC,SAAS,CAACF,GAAG,CAAC;EAC/B,MAAML,MAAM,GAAGM,QAAQ,CAACN,MAAM;EAC9B,IAAIA,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACpB,OAAOzB,MAAM,CAACiC,IAAI,CAChB,IAAAC,uBAAe,EAACH,QAAQ,EAAE,0CAA0CN,MAAM,EAAE,CAAC,CAC9E;EACH;EAEA,MAAMU,KAAK,GAAGJ,QAAQ,CAACK,OAAO,CAAC,GAAG,CAAC;EACnC,IAAID,KAAK,KAAK,CAAC,CAAC,KAAMA,KAAK,GAAGV,MAAM,GAAG,CAAC,IAAMU,KAAK,KAAKV,MAAM,GAAG,CAAC,IAAIM,QAAQ,CAACN,MAAM,GAAG,CAAC,CAAC,KAAK,GAAI,CAAC,EAAE;IACpG,OAAOzB,MAAM,CAACiC,IAAI,CAChB,IAAAC,uBAAe,EAACH,QAAQ,EAAE,iDAAiD,CAAC,CAC7E;EACH;EAEA,IAAI;IACF,MAAMM,aAAa,GAAGN,QAAQ,CAACO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGP,QAAQ,CAACO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAClF,MAAMZ,MAAM,GAAG,IAAIa,UAAU,CAAC,CAAC,IAAId,MAAM,GAAG,CAAC,CAAC,GAAGY,aAAa,CAAC;IAC/D,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAE6B,CAAC,GAAG,CAAC,EAAE7B,CAAC,GAAGc,MAAM,EAAEd,CAAC,IAAI,CAAC,EAAE6B,CAAC,IAAI,CAAC,EAAE;MACjD,MAAMC,MAAM,GAAGC,aAAa,CAACX,QAAQ,CAACY,UAAU,CAAChC,CAAC,CAAC,CAAC,IAAI,EAAE,GACxD+B,aAAa,CAACX,QAAQ,CAACY,UAAU,CAAChC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAC/C+B,aAAa,CAACX,QAAQ,CAACY,UAAU,CAAChC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAC9C+B,aAAa,CAACX,QAAQ,CAACY,UAAU,CAAChC,CAAC,GAAG,CAAC,CAAC,CAAC;MAE3Ce,MAAM,CAACc,CAAC,CAAC,GAAGC,MAAM,IAAI,EAAE;MACxBf,MAAM,CAACc,CAAC,GAAG,CAAC,CAAC,GAAIC,MAAM,IAAI,CAAC,GAAI,IAAI;MACpCf,MAAM,CAACc,CAAC,GAAG,CAAC,CAAC,GAAGC,MAAM,GAAG,IAAI;IAC/B;IAEA,OAAOzC,MAAM,CAAC4C,KAAK,CAAClB,MAAM,CAAC;EAC7B,CAAC,CAAC,OAAOtB,CAAC,EAAE;IACV,OAAOJ,MAAM,CAACiC,IAAI,CAChB,IAAAC,uBAAe,EAACH,QAAQ,EAAE3B,CAAC,YAAYyC,KAAK,GAAGzC,CAAC,CAAC0C,OAAO,GAAG,eAAe,CAAC,CAC5E;EACH;AACF,CAAC;AAED;AAAAlB,OAAA,CAAAC,MAAA,GAAAA,MAAA;AACO,MAAMG,SAAS,GAAIF,GAAW,IAAKA,GAAG,CAACiB,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;AAEpE;AAAAnB,OAAA,CAAAI,SAAA,GAAAA,SAAA;AACA,SAASU,aAAaA,CAACM,QAAgB;EACrC,IAAIA,QAAQ,IAAIC,WAAW,CAACxB,MAAM,EAAE;IAClC,MAAM,IAAIyB,SAAS,CAAC,qBAAqBC,MAAM,CAACC,YAAY,CAACJ,QAAQ,CAAC,EAAE,CAAC;EAC3E;EAEA,MAAMK,IAAI,GAAGJ,WAAW,CAACD,QAAQ,CAAC;EAClC,IAAIK,IAAI,KAAK,GAAG,EAAE;IAChB,MAAM,IAAIH,SAAS,CAAC,qBAAqBC,MAAM,CAACC,YAAY,CAACJ,QAAQ,CAAC,EAAE,CAAC;EAC3E;EAEA,OAAOK,IAAI;AACb;AAEA;AACA,MAAM1B,SAAS,GAAG,CAChB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;AAED;AACA,MAAMsB,WAAW,GAAG,CAClB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,EAAE,EACF,GAAG,EACH,GAAG,EACH,GAAG,EACH,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,GAAG,EACH,GAAG,EACH,GAAG,EACH,CAAC,EACD,GAAG,EACH,GAAG,EACH,GAAG,EACH,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,CACH", "ignoreList": []}