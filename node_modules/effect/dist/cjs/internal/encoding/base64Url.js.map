{"version": 3, "file": "base64Url.js", "names": ["Either", "_interopRequireWildcard", "require", "Base64", "_common", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "encode", "data", "replace", "exports", "decode", "str", "stripped", "stripCrlf", "length", "left", "DecodeException", "test", "sanitized"], "sources": ["../../../../src/internal/encoding/base64Url.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAA6C,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE7C;AACO,MAAMkB,MAAM,GAAIC,IAAgB,IACrCtB,MAAM,CAACqB,MAAM,CAACC,IAAI,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AAE/E;AAAAC,OAAA,CAAAH,MAAA,GAAAA,MAAA;AACO,MAAMI,MAAM,GAAIC,GAAW,IAAyD;EACzF,MAAMC,QAAQ,GAAG3B,MAAM,CAAC4B,SAAS,CAACF,GAAG,CAAC;EACtC,MAAMG,MAAM,GAAGF,QAAQ,CAACE,MAAM;EAC9B,IAAIA,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACpB,OAAOhC,MAAM,CAACiC,IAAI,CAChB,IAAAC,uBAAe,EAACJ,QAAQ,EAAE,4CAA4CE,MAAM,EAAE,CAAC,CAChF;EACH;EAEA,IAAI,CAAC,uBAAuB,CAACG,IAAI,CAACL,QAAQ,CAAC,EAAE;IAC3C,OAAO9B,MAAM,CAACiC,IAAI,CAAC,IAAAC,uBAAe,EAACJ,QAAQ,EAAE,eAAe,CAAC,CAAC;EAChE;EAEA;EACA,IAAIM,SAAS,GAAGJ,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAGF,QAAQ,IAAI,GAAGE,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAGF,QAAQ,GAAG,GAAGA,QAAQ;EACjGM,SAAS,GAAGA,SAAS,CAACV,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EAE3D,OAAOvB,MAAM,CAACyB,MAAM,CAACQ,SAAS,CAAC;AACjC,CAAC;AAAAT,OAAA,CAAAC,MAAA,GAAAA,MAAA", "ignoreList": []}