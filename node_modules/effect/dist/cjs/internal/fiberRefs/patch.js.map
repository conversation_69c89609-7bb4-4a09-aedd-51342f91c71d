{"version": 3, "file": "patch.js", "names": ["Arr", "_interopRequireWildcard", "require", "_Equal", "_Function", "fiberRefs_", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "OP_EMPTY", "exports", "OP_ADD", "OP_REMOVE", "OP_UPDATE", "OP_AND_THEN", "empty", "_tag", "diff", "oldValue", "newValue", "missingLocals", "Map", "locals", "patch", "fiberRef", "pairs", "entries", "headNonEmpty", "old", "undefined", "equals", "combine", "value", "delete", "dual", "self", "that", "first", "second", "fiberId", "fiberRefs", "patches", "of", "isNonEmptyReadonlyArray", "head", "tail", "tailNonEmpty", "updateAs", "delete_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prepend"], "sources": ["../../../../src/internal/fiberRefs/patch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAIA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAA6C,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE7C;AACO,MAAMkB,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAG,OAAgB;AAKxC;AACO,MAAME,MAAM,GAAAD,OAAA,CAAAC,MAAA,GAAG,KAAc;AAKpC;AACO,MAAMC,SAAS,GAAAF,OAAA,CAAAE,SAAA,GAAG,QAAiB;AAK1C;AACO,MAAMC,SAAS,GAAAH,OAAA,CAAAG,SAAA,GAAG,QAAiB;AAK1C;AACO,MAAMC,WAAW,GAAAJ,OAAA,CAAAI,WAAA,GAAG,SAAkB;AAK7C;AACO,MAAMC,KAAK,GAAAL,OAAA,CAAAK,KAAA,GAAmC;EACnDC,IAAI,EAAEP;CAC2B;AAEnC;AACO,MAAMQ,IAAI,GAAGA,CAClBC,QAA6B,EAC7BC,QAA6B,KACI;EACjC,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAACI,MAAM,CAAC;EAC9C,IAAIC,KAAK,GAAGR,KAAK;EACjB,KAAK,MAAM,CAACS,QAAQ,EAAEC,KAAK,CAAC,IAAIN,QAAQ,CAACG,MAAM,CAACI,OAAO,EAAE,EAAE;IACzD,MAAMP,QAAQ,GAAGnC,GAAG,CAAC2C,YAAY,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAMG,GAAG,GAAGR,aAAa,CAAClB,GAAG,CAACsB,QAAQ,CAAC;IACvC,IAAII,GAAG,KAAKC,SAAS,EAAE;MACrB,MAAMX,QAAQ,GAAGlC,GAAG,CAAC2C,YAAY,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzC,IAAI,CAAC,IAAAE,aAAM,EAACZ,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAC/BI,KAAK,GAAGQ,OAAO,CAAC;UACdf,IAAI,EAAEH,SAAS;UACfW,QAAQ;UACRD,KAAK,EAAEC,QAAQ,CAACP,IAAI,CAACC,QAAQ,EAAEC,QAAQ;SACxC,CAAC,CAACI,KAAK,CAAC;MACX;IACF,CAAC,MAAM;MACLA,KAAK,GAAGQ,OAAO,CAAC;QACdf,IAAI,EAAEL,MAAM;QACZa,QAAQ;QACRQ,KAAK,EAAEb;OACR,CAAC,CAACI,KAAK,CAAC;IACX;IACAH,aAAa,CAACa,MAAM,CAACT,QAAQ,CAAC;EAChC;EACA,KAAK,MAAM,CAACA,QAAQ,CAAC,IAAIJ,aAAa,CAACM,OAAO,EAAE,EAAE;IAChDH,KAAK,GAAGQ,OAAO,CAAC;MACdf,IAAI,EAAEJ,SAAS;MACfY;KACD,CAAC,CAACD,KAAK,CAAC;EACX;EACA,OAAOA,KAAK;AACd,CAAC;AAED;AAAAb,OAAA,CAAAO,IAAA,GAAAA,IAAA;AACO,MAAMc,OAAO,GAAArB,OAAA,CAAAqB,OAAA,gBAAG,IAAAG,cAAI,EAGzB,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,MAAM;EACpBpB,IAAI,EAAEF,WAAW;EACjBuB,KAAK,EAAEF,IAAI;EACXG,MAAM,EAAEF;CACT,CAAC,CAAC;AAEH;AACO,MAAMb,KAAK,GAAAb,OAAA,CAAAa,KAAA,gBAAG,IAAAW,cAAI,EAUvB,CAAC,EAAE,CAACC,IAAI,EAAEI,OAAO,EAAErB,QAAQ,KAAI;EAC/B,IAAIsB,SAAS,GAAwBtB,QAAQ;EAC7C,IAAIuB,OAAO,GAAiDzD,GAAG,CAAC0D,EAAE,CAACP,IAAI,CAAC;EACxE,OAAOnD,GAAG,CAAC2D,uBAAuB,CAACF,OAAO,CAAC,EAAE;IAC3C,MAAMG,IAAI,GAAG5D,GAAG,CAAC2C,YAAY,CAACc,OAAO,CAAC;IACtC,MAAMI,IAAI,GAAG7D,GAAG,CAAC8D,YAAY,CAACL,OAAO,CAAC;IACtC,QAAQG,IAAI,CAAC5B,IAAI;MACf,KAAKP,QAAQ;QAAE;UACbgC,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAKlC,MAAM;QAAE;UACX6B,SAAS,GAAGnD,UAAU,CAAC0D,QAAQ,CAACP,SAAS,EAAE;YACzCD,OAAO;YACPf,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ;YACvBQ,KAAK,EAAEY,IAAI,CAACZ;WACb,CAAC;UACFS,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAKjC,SAAS;QAAE;UACd4B,SAAS,GAAGnD,UAAU,CAAC2D,OAAO,CAACR,SAAS,EAAEI,IAAI,CAACpB,QAAQ,CAAC;UACxDiB,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAKhC,SAAS;QAAE;UACd,MAAMmB,KAAK,GAAG3C,UAAU,CAAC4D,YAAY,CAACT,SAAS,EAAEI,IAAI,CAACpB,QAAQ,CAAC;UAC/DgB,SAAS,GAAGnD,UAAU,CAAC0D,QAAQ,CAACP,SAAS,EAAE;YACzCD,OAAO;YACPf,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ;YACvBQ,KAAK,EAAEY,IAAI,CAACpB,QAAQ,CAACD,KAAK,CAACqB,IAAI,CAACrB,KAAK,CAAC,CAACS,KAAK;WAC7C,CAAC;UACFS,OAAO,GAAGI,IAAI;UACd;QACF;MACA,KAAK/B,WAAW;QAAE;UAChB2B,OAAO,GAAGzD,GAAG,CAACkE,OAAO,CAACN,IAAI,CAACP,KAAK,CAAC,CAACrD,GAAG,CAACkE,OAAO,CAACN,IAAI,CAACN,MAAM,CAAC,CAACO,IAAI,CAAC,CAAC;UACjE;QACF;IACF;EACF;EACA,OAAOL,SAAS;AAClB,CAAC,CAAC", "ignoreList": []}