{"version": 3, "file": "runtimeFlags.js", "names": ["_Function", "require", "<PERSON><PERSON><PERSON><PERSON>", "_interopRequireWildcard", "runtimeFlagsPatch", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "None", "exports", "Interruption", "OpSupervision", "RuntimeMetrics", "WindDown", "CooperativeYielding", "allFlags", "print", "flag", "cooperativeYielding", "self", "isEnabled", "disable", "dual", "disableAll", "flags", "enable", "enableAll", "interruptible", "interruption", "windDown", "isDisabled", "make", "reduce", "a", "b", "none", "opSupervision", "render", "active", "for<PERSON>ach", "push", "join", "runtimeMetrics", "toSet", "Set", "filter", "enabledSet", "enabled", "disabledSet", "diff", "that", "patch", "invert", "renderPatch", "Array", "from", "map", "disabled", "differ", "empty", "oldValue", "newValue", "combine", "first", "second", "and<PERSON><PERSON>", "_patch"], "sources": ["../../../src/internal/runtimeFlags.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AAGA,IAAAC,cAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAD,uBAAA,CAAAF,OAAA;AAA2D,SAAAE,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE3D;AACO,MAAMkB,IAAI,GAAAC,OAAA,CAAAD,IAAA,GAA6B,CAA6B;AAE3E;AACO,MAAME,YAAY,GAAAD,OAAA,CAAAC,YAAA,GAA6B,CAAC,IAAI,CAA6B;AAExF;AACO,MAAMC,aAAa,GAAAF,OAAA,CAAAE,aAAA,GAA6B,CAAC,IAAI,CAA6B;AAEzF;AACO,MAAMC,cAAc,GAAAH,OAAA,CAAAG,cAAA,GAA6B,CAAC,IAAI,CAA6B;AAE1F;AACO,MAAMC,QAAQ,GAAAJ,OAAA,CAAAI,QAAA,GAA6B,CAAC,IAAI,CAA6B;AAEpF;AACO,MAAMC,mBAAmB,GAAAL,OAAA,CAAAK,mBAAA,GAA6B,CAAC,IAAI,CAA6B;AAE/F;AACO,MAAMC,QAAQ,GAAAN,OAAA,CAAAM,QAAA,GAA4C,CAC/DP,IAAI,EACJE,YAAY,EACZC,aAAa,EACbC,cAAc,EACdC,QAAQ,EACRC,mBAAmB,CACpB;AAED,MAAME,KAAK,GAAIC,IAA8B,IAAI;EAC/C,QAAQA,IAAI;IACV,KAAKH,mBAAmB;MAAE;QACxB,OAAO,qBAAqB;MAC9B;IACA,KAAKD,QAAQ;MAAE;QACb,OAAO,UAAU;MACnB;IACA,KAAKD,cAAc;MAAE;QACnB,OAAO,gBAAgB;MACzB;IACA,KAAKD,aAAa;MAAE;QAClB,OAAO,eAAe;MACxB;IACA,KAAKD,YAAY;MAAE;QACjB,OAAO,cAAc;MACvB;IACA,KAAKF,IAAI;MAAE;QACT,OAAO,MAAM;MACf;EACF;AACF,CAAC;AAED;AACO,MAAMU,mBAAmB,GAAIC,IAA+B,IAAcC,SAAS,CAACD,IAAI,EAAEL,mBAAmB,CAAC;AAErH;AAAAL,OAAA,CAAAS,mBAAA,GAAAA,mBAAA;AACO,MAAMG,OAAO,GAAAZ,OAAA,CAAAY,OAAA,gBAAG,IAAAC,cAAI,EAGzB,CAAC,EAAE,CAACH,IAAI,EAAEF,IAAI,KAAME,IAAI,GAAG,CAACF,IAAkC,CAAC;AAEjE;AACO,MAAMM,UAAU,GAAAd,OAAA,CAAAc,UAAA,gBAAG,IAAAD,cAAI,EAG5B,CAAC,EAAE,CAACH,IAAI,EAAEK,KAAK,KAAML,IAAI,GAAG,CAACK,KAAmC,CAAC;AAEnE;AACO,MAAMC,MAAM,GAAAhB,OAAA,CAAAgB,MAAA,gBAAG,IAAAH,cAAI,EAGxB,CAAC,EAAE,CAACH,IAAI,EAAEF,IAAI,KAAME,IAAI,GAAGF,IAAkC,CAAC;AAEhE;AACO,MAAMS,SAAS,GAAAjB,OAAA,CAAAiB,SAAA,gBAAG,IAAAJ,cAAI,EAG3B,CAAC,EAAE,CAACH,IAAI,EAAEK,KAAK,KAAML,IAAI,GAAGK,KAAmC,CAAC;AAElE;AACO,MAAMG,aAAa,GAAIR,IAA+B,IAAcS,YAAY,CAACT,IAAI,CAAC,IAAI,CAACU,QAAQ,CAACV,IAAI,CAAC;AAEhH;AAAAV,OAAA,CAAAkB,aAAA,GAAAA,aAAA;AACO,MAAMC,YAAY,GAAIT,IAA+B,IAAcC,SAAS,CAACD,IAAI,EAAET,YAAY,CAAC;AAEvG;AAAAD,OAAA,CAAAmB,YAAA,GAAAA,YAAA;AACO,MAAME,UAAU,GAAArB,OAAA,CAAAqB,UAAA,gBAAG,IAAAR,cAAI,EAG5B,CAAC,EAAE,CAACH,IAAI,EAAEF,IAAI,KAAK,CAACG,SAAS,CAACD,IAAI,EAAEF,IAAI,CAAC,CAAC;AAE5C;AACO,MAAMG,SAAS,GAAAX,OAAA,CAAAW,SAAA,gBAAG,IAAAE,cAAI,EAG3B,CAAC,EAAE,CAACH,IAAI,EAAEF,IAAI,KAAK,CAACE,IAAI,GAAGF,IAAI,MAAM,CAAC,CAAC;AAEzC;AACO,MAAMc,IAAI,GAAGA,CAAC,GAAGP,KAA8C,KACpEA,KAAK,CAACQ,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAA8B;AAE/D;AAAAzB,OAAA,CAAAsB,IAAA,GAAAA,IAAA;AACO,MAAMI,IAAI,GAAA1B,OAAA,CAAA0B,IAAA,gBAA8BJ,IAAI,CAACvB,IAAI,CAAC;AAEzD;AACO,MAAM4B,aAAa,GAAIjB,IAA+B,IAAcC,SAAS,CAACD,IAAI,EAAER,aAAa,CAAC;AAEzG;AAAAF,OAAA,CAAA2B,aAAA,GAAAA,aAAA;AACO,MAAMC,MAAM,GAAIlB,IAA+B,IAAY;EAChE,MAAMmB,MAAM,GAAkB,EAAE;EAChCvB,QAAQ,CAACwB,OAAO,CAAEtB,IAAI,IAAI;IACxB,IAAIG,SAAS,CAACD,IAAI,EAAEF,IAAI,CAAC,EAAE;MACzBqB,MAAM,CAACE,IAAI,CAAC,GAAGxB,KAAK,CAACC,IAAI,CAAC,EAAE,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,OAAO,gBAAgBqB,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,GAAG;AAC7C,CAAC;AAED;AAAAhC,OAAA,CAAA4B,MAAA,GAAAA,MAAA;AACO,MAAMK,cAAc,GAAIvB,IAA+B,IAAcC,SAAS,CAACD,IAAI,EAAEP,cAAc,CAAC;AAE3G;AAAAH,OAAA,CAAAiC,cAAA,GAAAA,cAAA;AACO,MAAMC,KAAK,GAAIxB,IAA+B,IACnD,IAAIyB,GAAG,CAAC7B,QAAQ,CAAC8B,MAAM,CAAE5B,IAAI,IAAKG,SAAS,CAACD,IAAI,EAAEF,IAAI,CAAC,CAAC,CAAC;AAAAR,OAAA,CAAAkC,KAAA,GAAAA,KAAA;AAEpD,MAAMd,QAAQ,GAAIV,IAA+B,IAAcC,SAAS,CAACD,IAAI,EAAEN,QAAQ,CAAC;AAE/F;AAEA;AAAAJ,OAAA,CAAAoB,QAAA,GAAAA,QAAA;AACO,MAAMiB,UAAU,GAAI3B,IAAyC,IAClEwB,KAAK,CAAEvD,iBAAiB,CAACkD,MAAM,CAACnB,IAAI,CAAC,GAAG/B,iBAAiB,CAAC2D,OAAO,CAAC5B,IAAI,CAA+B,CAAC;AAExG;AAAAV,OAAA,CAAAqC,UAAA,GAAAA,UAAA;AACO,MAAME,WAAW,GAAI7B,IAAyC,IACnEwB,KAAK,CAAEvD,iBAAiB,CAACkD,MAAM,CAACnB,IAAI,CAAC,GAAG,CAAC/B,iBAAiB,CAAC2D,OAAO,CAAC5B,IAAI,CAA+B,CAAC;AAEzG;AAAAV,OAAA,CAAAuC,WAAA,GAAAA,WAAA;AACO,MAAMC,IAAI,GAAAxC,OAAA,CAAAwC,IAAA,gBAAG,IAAA3B,cAAI,EAGtB,CAAC,EAAE,CAACH,IAAI,EAAE+B,IAAI,KAAK9D,iBAAiB,CAAC2C,IAAI,CAACZ,IAAI,GAAG+B,IAAI,EAAEA,IAAI,CAAC,CAAC;AAE/D;AACO,MAAMC,KAAK,GAAA1C,OAAA,CAAA0C,KAAA,gBAAG,IAAA7B,cAAI,EAGvB,CAAC,EAAE,CAACH,IAAI,EAAEgC,KAAK,KAEZhC,IAAI,IAAI/B,iBAAiB,CAACgE,MAAM,CAAChE,iBAAiB,CAACkD,MAAM,CAACa,KAAK,CAAC,CAAC,GAAG/D,iBAAiB,CAAC2D,OAAO,CAACI,KAAK,CAAC,CAAC,GACrG/D,iBAAiB,CAACkD,MAAM,CAACa,KAAK,CAAC,GAAG/D,iBAAiB,CAAC2D,OAAO,CAACI,KAAK,CACtC,CAAC;AAEjC;AACO,MAAME,WAAW,GAAIlC,IAAyC,IAAY;EAC/E,MAAM4B,OAAO,GAAGO,KAAK,CAACC,IAAI,CAACT,UAAU,CAAC3B,IAAI,CAAC,CAAC,CACzCqC,GAAG,CAAEvC,IAAI,IAAKD,KAAK,CAACC,IAAI,CAAC,CAAC,CAC1BwB,IAAI,CAAC,IAAI,CAAC;EACb,MAAMgB,QAAQ,GAAGH,KAAK,CAACC,IAAI,CAACP,WAAW,CAAC7B,IAAI,CAAC,CAAC,CAC3CqC,GAAG,CAAEvC,IAAI,IAAKD,KAAK,CAACC,IAAI,CAAC,CAAC,CAC1BwB,IAAI,CAAC,IAAI,CAAC;EACb,OAAO,gCAAgCM,OAAO,kBAAkBU,QAAQ,IAAI;AAC9E,CAAC;AAED;AAAAhD,OAAA,CAAA4C,WAAA,GAAAA,WAAA;AACO,MAAMK,MAAM,GAAAjD,OAAA,CAAAiD,MAAA,gBAAkFxE,cAAc,CAChH6C,IAAI,CAAC;EACJ4B,KAAK,EAAEvE,iBAAiB,CAACuE,KAAK;EAC9BV,IAAI,EAAEA,CAACW,QAAQ,EAAEC,QAAQ,KAAKZ,IAAI,CAACW,QAAQ,EAAEC,QAAQ,CAAC;EACtDC,OAAO,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK5E,iBAAiB,CAAC6E,OAAO,CAACD,MAAM,CAAC,CAACD,KAAK,CAAC;EACpEZ,KAAK,EAAEA,CAACe,MAAM,EAAEN,QAAQ,KAAKT,KAAK,CAACS,QAAQ,EAAEM,MAAM;CACpD,CAAC", "ignoreList": []}