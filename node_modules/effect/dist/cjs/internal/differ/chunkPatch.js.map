{"version": 3, "file": "chunkPatch.js", "names": ["Chunk", "_interopRequireWildcard", "require", "Equal", "_Function", "Dual", "Data", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ChunkPatchTypeId", "exports", "Symbol", "for", "variance", "a", "PatchProto", "Structural", "prototype", "_Value", "_Patch", "EmptyProto", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "AppendProto", "makeAppend", "values", "SliceProto", "makeSlice", "from", "until", "UpdateProto", "makeUpdate", "index", "patch", "diff", "options", "oldValue", "length", "newValue", "oldElement", "unsafeGet", "newElement", "valuePatch", "differ", "equals", "pipe", "combine", "drop", "dual", "self", "that", "chunk", "patches", "of", "isNonEmpty", "head", "headNonEmpty", "tail", "tailNonEmpty", "prepend", "appendAll", "array", "toReadonlyArray", "unsafeFromArray", "slice"], "sources": ["../../../../src/internal/differ/chunkPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAyC,IAAAG,IAAA,GAAAD,SAAA;AAEzC,IAAAE,IAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAkC,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAElC;AACO,MAAMkB,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,gBAA+BE,MAAM,CAACC,GAAG,CACpE,yBAAyB,CACI;AAE/B,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA,MAAMC,UAAU,GAAG;EACjB,GAAG1B,IAAI,CAAC2B,UAAU,CAACC,SAAS;EAC5B,CAACR,gBAAgB,GAAG;IAClBS,MAAM,EAAEL,QAAQ;IAChBM,MAAM,EAAEN;;CAEX;AAMD,MAAMO,UAAU,gBAAGd,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC1DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGlB,MAAM,CAACgB,MAAM,CAACF,UAAU,CAAC;AAExC;;;AAGO,MAAMK,KAAK,GAAGA,CAAA,KAA6DD,MAAM;AAAAd,OAAA,CAAAe,KAAA,GAAAA,KAAA;AAQxF,MAAMC,YAAY,gBAAGpB,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC5DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMI,WAAW,GAAGA,CAClBC,KAA8C,EAC9CC,MAA+C,KACJ;EAC3C,MAAMjC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACI,YAAY,CAAC;EACrC9B,CAAC,CAACgC,KAAK,GAAGA,KAAK;EACfhC,CAAC,CAACiC,MAAM,GAAGA,MAAM;EACjB,OAAOjC,CAAC;AACV,CAAC;AAOD,MAAMkC,WAAW,gBAAGxB,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC3DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMQ,UAAU,GAAkBC,MAA0B,IAA6C;EACvG,MAAMpC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACQ,WAAW,CAAC;EACpClC,CAAC,CAACoC,MAAM,GAAGA,MAAM;EACjB,OAAOpC,CAAC;AACV,CAAC;AAQD,MAAMqC,UAAU,gBAAG3B,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC1DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMW,SAAS,GAAGA,CAAeC,IAAY,EAAEC,KAAa,KAA6C;EACvG,MAAMxC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACW,UAAU,CAAC;EACnCrC,CAAC,CAACuC,IAAI,GAAGA,IAAI;EACbvC,CAAC,CAACwC,KAAK,GAAGA,KAAK;EACf,OAAOxC,CAAC;AACV,CAAC;AAQD,MAAMyC,WAAW,gBAAG/B,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC3DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMe,UAAU,GAAGA,CAAeC,KAAa,EAAEC,KAAY,KAA6C;EACxG,MAAM5C,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACe,WAAW,CAAC;EACpCzC,CAAC,CAAC2C,KAAK,GAAGA,KAAK;EACf3C,CAAC,CAAC4C,KAAK,GAAGA,KAAK;EACf,OAAO5C,CAAC;AACV,CAAC;AASD;AACO,MAAM6C,IAAI,GACfC,OAIC,IAC0C;EAC3C,IAAI7C,CAAC,GAAG,CAAC;EACT,IAAI2C,KAAK,GAAGf,KAAK,EAAgB;EACjC,OAAO5B,CAAC,GAAG6C,OAAO,CAACC,QAAQ,CAACC,MAAM,IAAI/C,CAAC,GAAG6C,OAAO,CAACG,QAAQ,CAACD,MAAM,EAAE;IACjE,MAAME,UAAU,GAAG/D,KAAK,CAACgE,SAAS,CAAClD,CAAC,CAAC,CAAC6C,OAAO,CAACC,QAAQ,CAAC;IACvD,MAAMK,UAAU,GAAGjE,KAAK,CAACgE,SAAS,CAAClD,CAAC,CAAC,CAAC6C,OAAO,CAACG,QAAQ,CAAC;IACvD,MAAMI,UAAU,GAAGP,OAAO,CAACQ,MAAM,CAACT,IAAI,CAACK,UAAU,EAAEE,UAAU,CAAC;IAC9D,IAAI,CAAC9D,KAAK,CAACiE,MAAM,CAACF,UAAU,EAAEP,OAAO,CAACQ,MAAM,CAACzB,KAAK,CAAC,EAAE;MACnDe,KAAK,GAAG,IAAAY,cAAI,EAACZ,KAAK,EAAEa,OAAO,CAACf,UAAU,CAACzC,CAAC,EAAEoD,UAAU,CAAC,CAAC,CAAC;IACzD;IACApD,CAAC,GAAGA,CAAC,GAAG,CAAC;EACX;EACA,IAAIA,CAAC,GAAG6C,OAAO,CAACC,QAAQ,CAACC,MAAM,EAAE;IAC/BJ,KAAK,GAAG,IAAAY,cAAI,EAACZ,KAAK,EAAEa,OAAO,CAACnB,SAAS,CAAC,CAAC,EAAErC,CAAC,CAAC,CAAC,CAAC;EAC/C;EACA,IAAIA,CAAC,GAAG6C,OAAO,CAACG,QAAQ,CAACD,MAAM,EAAE;IAC/BJ,KAAK,GAAG,IAAAY,cAAI,EAACZ,KAAK,EAAEa,OAAO,CAACtB,UAAU,CAAChD,KAAK,CAACuE,IAAI,CAACzD,CAAC,CAAC,CAAC6C,OAAO,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC3E;EACA,OAAOL,KAAK;AACd,CAAC;AAED;AAAA9B,OAAA,CAAA+B,IAAA,GAAAA,IAAA;AACO,MAAMY,OAAO,GAAA3C,OAAA,CAAA2C,OAAA,gBAAGjE,IAAI,CAACmE,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAK9B,WAAW,CAAC6B,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACO,MAAMjB,KAAK,GAAA9B,OAAA,CAAA8B,KAAA,gBAAGpD,IAAI,CAACmE,IAAI,CAU5B,CAAC,EAAE,CACHC,IAA6C,EAC7Cb,QAA4B,EAC5BO,MAAmC,KACjC;EACF,IAAKM,IAAoB,CAACjC,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOoB,QAAQ;EACjB;EACA,IAAIe,KAAK,GAAGf,QAAQ;EACpB,IAAIgB,OAAO,GAAyD5E,KAAK,CAAC6E,EAAE,CAACJ,IAAI,CAAC;EAClF,OAAOzE,KAAK,CAAC8E,UAAU,CAACF,OAAO,CAAC,EAAE;IAChC,MAAMG,IAAI,GAAgB/E,KAAK,CAACgF,YAAY,CAACJ,OAAO,CAAgB;IACpE,MAAMK,IAAI,GAAGjF,KAAK,CAACkF,YAAY,CAACN,OAAO,CAAC;IACxC,QAAQG,IAAI,CAACvC,IAAI;MACf,KAAK,OAAO;QAAE;UACZoC,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdL,OAAO,GAAG5E,KAAK,CAACmF,OAAO,CAACJ,IAAI,CAAClC,KAAK,CAAC,CAAC7C,KAAK,CAACmF,OAAO,CAACJ,IAAI,CAACjC,MAAM,CAAC,CAACmC,IAAI,CAAC,CAAC;UACrE;QACF;MACA,KAAK,QAAQ;QAAE;UACbN,KAAK,GAAG3E,KAAK,CAACoF,SAAS,CAACL,IAAI,CAAC9B,MAAM,CAAC,CAAC0B,KAAK,CAAC;UAC3CC,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,OAAO;QAAE;UACZ,MAAMI,KAAK,GAAGrF,KAAK,CAACsF,eAAe,CAACX,KAAK,CAAC;UAC1CA,KAAK,GAAG3E,KAAK,CAACuF,eAAe,CAACF,KAAK,CAACG,KAAK,CAACT,IAAI,CAAC3B,IAAI,EAAE2B,IAAI,CAAC1B,KAAK,CAAC,CAAC;UACjEuB,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACb,MAAMI,KAAK,GAAGrF,KAAK,CAACsF,eAAe,CAACX,KAAK,CAAiB;UAC1DU,KAAK,CAACN,IAAI,CAACvB,KAAK,CAAC,GAAGW,MAAM,CAACV,KAAK,CAACsB,IAAI,CAACtB,KAAK,EAAE4B,KAAK,CAACN,IAAI,CAACvB,KAAK,CAAE,CAAC;UAChEmB,KAAK,GAAG3E,KAAK,CAACuF,eAAe,CAACF,KAAK,CAAC;UACpCT,OAAO,GAAGK,IAAI;UACd;QACF;IACF;EACF;EACA,OAAON,KAAK;AACd,CAAC,CAAC", "ignoreList": []}