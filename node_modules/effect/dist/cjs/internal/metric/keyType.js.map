{"version": 3, "file": "keyType.js", "names": ["Duration", "_interopRequireWildcard", "require", "Equal", "_Function", "Hash", "_Pipeable", "_Predicate", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "MetricKeyTypeSymbolKey", "MetricKeyTypeTypeId", "exports", "Symbol", "for", "CounterKeyTypeSymbolKey", "CounterKeyTypeTypeId", "FrequencyKeyTypeSymbolKey", "FrequencyKeyTypeTypeId", "GaugeKeyTypeSymbolKey", "GaugeKeyTypeTypeId", "HistogramKeyTypeSymbolKey", "HistogramKeyTypeTypeId", "SummaryKeyTypeSymbolKey", "SummaryKeyTypeTypeId", "metricKeyTypeVariance", "_In", "_", "_Out", "CounterKeyType", "incremental", "bigint", "constructor", "_hash", "string", "symbol", "that", "isCounterKey", "pipe", "pipeArguments", "arguments", "FrequencyKeyTypeHash", "FrequencyKeyType", "preregisteredWords", "isFrequencyKey", "GaugeKeyTypeHash", "GaugeKeyType", "isGaugeKey", "HistogramKeyType", "boundaries", "combine", "hash", "isHistogramKey", "equals", "SummaryKeyType", "maxAge", "maxSize", "error", "quantiles", "array", "isSummary<PERSON>ey", "counter", "options", "frequency", "gauge", "histogram", "summary", "decode", "isMetricKeyType", "u", "hasProperty"], "sources": ["../../../../src/internal/metric/keyType.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAGA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAAgD,SAAAD,wBAAAO,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAO,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEhD;AACA,MAAMkB,sBAAsB,GAAG,sBAAsB;AAErD;AACO,MAAMC,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,gBAAsCE,MAAM,CAACC,GAAG,CAC9EJ,sBAAsB,CACc;AAEtC;AACA,MAAMK,uBAAuB,GAAG,8BAA8B;AAE9D;AACO,MAAMC,oBAAoB,GAAAJ,OAAA,CAAAI,oBAAA,gBAAuCH,MAAM,CAACC,GAAG,CAChFC,uBAAuB,CACc;AAEvC;AACA,MAAME,yBAAyB,GAAG,gCAAgC;AAElE;AACO,MAAMC,sBAAsB,GAAAN,OAAA,CAAAM,sBAAA,gBAAyCL,MAAM,CAACC,GAAG,CACpFG,yBAAyB,CACc;AAEzC;AACA,MAAME,qBAAqB,GAAG,4BAA4B;AAE1D;AACO,MAAMC,kBAAkB,GAAAR,OAAA,CAAAQ,kBAAA,gBAAqCP,MAAM,CAACC,GAAG,CAC5EK,qBAAqB,CACc;AAErC;AACA,MAAME,yBAAyB,GAAG,gCAAgC;AAElE;AACO,MAAMC,sBAAsB,GAAAV,OAAA,CAAAU,sBAAA,gBAAyCT,MAAM,CAACC,GAAG,CACpFO,yBAAyB,CACc;AAEzC;AACA,MAAME,uBAAuB,GAAG,8BAA8B;AAE9D;AACO,MAAMC,oBAAoB,GAAAZ,OAAA,CAAAY,oBAAA,gBAAuCX,MAAM,CAACC,GAAG,CAChFS,uBAAuB,CACc;AAEvC,MAAME,qBAAqB,GAAG;EAC5B;EACAC,GAAG,EAAGC,CAAU,IAAKA,CAAC;EACtB;EACAC,IAAI,EAAGD,CAAQ,IAAKA;CACrB;AAED;AACA,MAAME,cAAc;EAGGC,WAAA;EAA+BC,MAAA;EAF3C,CAACpB,mBAAmB,IAAIc,qBAAqB;EAC7C,CAACT,oBAAoB,IAAwCA,oBAAoB;EAC1FgB,YAAqBF,WAAoB,EAAWC,MAAe;IAA9C,KAAAD,WAAW,GAAXA,WAAW;IAAoB,KAAAC,MAAM,GAANA,MAAM;IACxD,IAAI,CAACE,KAAK,GAAG7C,IAAI,CAAC8C,MAAM,CAACnB,uBAAuB,CAAC;EACnD;EACSkB,KAAK;EACd,CAAC7C,IAAI,CAAC+C,MAAM,IAAC;IACX,OAAO,IAAI,CAACF,KAAK;EACnB;EACA,CAAC/C,KAAK,CAACiD,MAAM,EAAEC,IAAa;IAC1B,OAAOC,YAAY,CAACD,IAAI,CAAC;EAC3B;EACAE,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF,MAAMC,oBAAoB,gBAAGrD,IAAI,CAAC8C,MAAM,CAACjB,yBAAyB,CAAC;AAEnE;AACA,MAAMyB,gBAAgB;EAGCC,kBAAA;EAFZ,CAAChC,mBAAmB,IAAIc,qBAAqB;EAC7C,CAACP,sBAAsB,IAA0CA,sBAAsB;EAChGc,YAAqBW,kBAAyC;IAAzC,KAAAA,kBAAkB,GAAlBA,kBAAkB;EAA0B;EACjE,CAACvD,IAAI,CAAC+C,MAAM,IAAC;IACX,OAAOM,oBAAoB;EAC7B;EACA,CAACvD,KAAK,CAACiD,MAAM,EAAEC,IAAa;IAC1B,OAAOQ,cAAc,CAACR,IAAI,CAAC;EAC7B;EACAE,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF,MAAMK,gBAAgB,gBAAGzD,IAAI,CAAC8C,MAAM,CAACf,qBAAqB,CAAC;AAE3D;AACA,MAAM2B,YAAY;EAGKf,MAAA;EAFZ,CAACpB,mBAAmB,IAAIc,qBAAqB;EAC7C,CAACL,kBAAkB,IAAsCA,kBAAkB;EACpFY,YAAqBD,MAAe;IAAf,KAAAA,MAAM,GAANA,MAAM;EAAY;EACvC,CAAC3C,IAAI,CAAC+C,MAAM,IAAC;IACX,OAAOU,gBAAgB;EACzB;EACA,CAAC3D,KAAK,CAACiD,MAAM,EAAEC,IAAa;IAC1B,OAAOW,UAAU,CAACX,IAAI,CAAC;EACzB;EACAE,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AACM,MAAOQ,gBAAgB;EAGNC,UAAA;EAFZ,CAACtC,mBAAmB,IAAIc,qBAAqB;EAC7C,CAACH,sBAAsB,IAA0CA,sBAAsB;EAChGU,YAAqBiB,UAA6C;IAA7C,KAAAA,UAAU,GAAVA,UAAU;IAC7B,IAAI,CAAChB,KAAK,GAAG,IAAAK,cAAI,EACflD,IAAI,CAAC8C,MAAM,CAACb,yBAAyB,CAAC,EACtCjC,IAAI,CAAC8D,OAAO,CAAC9D,IAAI,CAAC+D,IAAI,CAAC,IAAI,CAACF,UAAU,CAAC,CAAC,CACzC;EACH;EACShB,KAAK;EACd,CAAC7C,IAAI,CAAC+C,MAAM,IAAC;IACX,OAAO,IAAI,CAACF,KAAK;EACnB;EACA,CAAC/C,KAAK,CAACiD,MAAM,EAAEC,IAAa;IAC1B,OAAOgB,cAAc,CAAChB,IAAI,CAAC,IAAIlD,KAAK,CAACmE,MAAM,CAAC,IAAI,CAACJ,UAAU,EAAEb,IAAI,CAACa,UAAU,CAAC;EAC/E;EACAX,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AAAA5B,OAAA,CAAAoC,gBAAA,GAAAA,gBAAA;AACA,MAAMM,cAAc;EAIPC,MAAA;EACAC,OAAA;EACAC,KAAA;EACAC,SAAA;EANF,CAAC/C,mBAAmB,IAAIc,qBAAqB;EAC7C,CAACD,oBAAoB,IAAwCA,oBAAoB;EAC1FQ,YACWuB,MAAyB,EACzBC,OAAe,EACfC,KAAa,EACbC,SAAgC;IAHhC,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;IAElB,IAAI,CAACzB,KAAK,GAAG,IAAAK,cAAI,EACflD,IAAI,CAAC8C,MAAM,CAACX,uBAAuB,CAAC,EACpCnC,IAAI,CAAC8D,OAAO,CAAC9D,IAAI,CAAC+D,IAAI,CAAC,IAAI,CAACI,MAAM,CAAC,CAAC,EACpCnE,IAAI,CAAC8D,OAAO,CAAC9D,IAAI,CAAC+D,IAAI,CAAC,IAAI,CAACK,OAAO,CAAC,CAAC,EACrCpE,IAAI,CAAC8D,OAAO,CAAC9D,IAAI,CAAC+D,IAAI,CAAC,IAAI,CAACM,KAAK,CAAC,CAAC,EACnCrE,IAAI,CAAC8D,OAAO,CAAC9D,IAAI,CAACuE,KAAK,CAAC,IAAI,CAACD,SAAS,CAAC,CAAC,CACzC;EACH;EACSzB,KAAK;EACd,CAAC7C,IAAI,CAAC+C,MAAM,IAAC;IACX,OAAO,IAAI,CAACF,KAAK;EACnB;EACA,CAAC/C,KAAK,CAACiD,MAAM,EAAEC,IAAa;IAC1B,OAAOwB,YAAY,CAACxB,IAAI,CAAC,IACvBlD,KAAK,CAACmE,MAAM,CAAC,IAAI,CAACE,MAAM,EAAEnB,IAAI,CAACmB,MAAM,CAAC,IACtC,IAAI,CAACC,OAAO,KAAKpB,IAAI,CAACoB,OAAO,IAC7B,IAAI,CAACC,KAAK,KAAKrB,IAAI,CAACqB,KAAK,IACzBvE,KAAK,CAACmE,MAAM,CAAC,IAAI,CAACK,SAAS,EAAEtB,IAAI,CAACsB,SAAS,CAAC;EAChD;EACApB,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AACO,MAAMqB,OAAO,GAGOC,OAAO,IAChC,IAAIjC,cAAc,CAChBiC,OAAO,EAAEhC,WAAW,IAAI,KAAK,EAC7BgC,OAAO,EAAE/B,MAAM,IAAI,KAAK,CACzB;AAEH;AAAAnB,OAAA,CAAAiD,OAAA,GAAAA,OAAA;AACO,MAAME,SAAS,GAAID,OAEzB,IAA4C,IAAIpB,gBAAgB,CAACoB,OAAO,EAAEnB,kBAAkB,IAAI,EAAE,CAAC;AAEpG;AAAA/B,OAAA,CAAAmD,SAAA,GAAAA,SAAA;AACO,MAAMC,KAAK,GAEOF,OAAO,IAC9B,IAAIhB,YAAY,CACdgB,OAAO,EAAE/B,MAAM,IAAI,KAAK,CACzB;AAEH;AAAAnB,OAAA,CAAAoD,KAAA,GAAAA,KAAA;AACO,MAAMC,SAAS,GAAIhB,UAA6C,IAA2C;EAChH,OAAO,IAAID,gBAAgB,CAACC,UAAU,CAAC;AACzC,CAAC;AAED;AAAArC,OAAA,CAAAqD,SAAA,GAAAA,SAAA;AACO,MAAMC,OAAO,GAClBJ,OAKC,IACsC;EACvC,OAAO,IAAIR,cAAc,CAACvE,QAAQ,CAACoF,MAAM,CAACL,OAAO,CAACP,MAAM,CAAC,EAAEO,OAAO,CAACN,OAAO,EAAEM,OAAO,CAACL,KAAK,EAAEK,OAAO,CAACJ,SAAS,CAAC;AAC/G,CAAC;AAED;AAAA9C,OAAA,CAAAsD,OAAA,GAAAA,OAAA;AACO,MAAME,eAAe,GAAIC,CAAU,IACxC,IAAAC,sBAAW,EAACD,CAAC,EAAE1D,mBAAmB,CAAC;AAErC;AAAAC,OAAA,CAAAwD,eAAA,GAAAA,eAAA;AACO,MAAM/B,YAAY,GAAIgC,CAAU,IACrC,IAAAC,sBAAW,EAACD,CAAC,EAAErD,oBAAoB,CAAC;AAEtC;AAAAJ,OAAA,CAAAyB,YAAA,GAAAA,YAAA;AACO,MAAMO,cAAc,GAAIyB,CAAU,IACvC,IAAAC,sBAAW,EAACD,CAAC,EAAEnD,sBAAsB,CAAC;AAExC;AAAAN,OAAA,CAAAgC,cAAA,GAAAA,cAAA;AACO,MAAMG,UAAU,GAAIsB,CAAU,IACnC,IAAAC,sBAAW,EAACD,CAAC,EAAEjD,kBAAkB,CAAC;AAEpC;AAAAR,OAAA,CAAAmC,UAAA,GAAAA,UAAA;AACO,MAAMK,cAAc,GAAIiB,CAAU,IACvC,IAAAC,sBAAW,EAACD,CAAC,EAAE/C,sBAAsB,CAAC;AAExC;AAAAV,OAAA,CAAAwC,cAAA,GAAAA,cAAA;AACO,MAAMQ,YAAY,GAAIS,CAAU,IACrC,IAAAC,sBAAW,EAACD,CAAC,EAAE7C,oBAAoB,CAAC;AAAAZ,OAAA,CAAAgD,YAAA,GAAAA,YAAA", "ignoreList": []}