{"version": 3, "file": "key.js", "names": ["Arr", "_interopRequireWildcard", "require", "Equal", "_Function", "Hash", "Option", "_Pipeable", "_Predicate", "metricKeyType", "metricLabel", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "MetricKeySymbolKey", "MetricKeyTypeId", "exports", "Symbol", "for", "metricKeyVariance", "_Type", "_", "arrayEquivilence", "getEquivalence", "equals", "MetricKeyImpl", "name", "keyType", "description", "tags", "constructor", "_hash", "pipe", "string", "combine", "hash", "array", "symbol", "u", "isMetricKey", "pipeArguments", "arguments", "hasProperty", "counter", "options", "fromNullable", "frequency", "gauge", "histogram", "boundaries", "summary", "tagged", "dual", "self", "key", "value", "taggedWithL<PERSON><PERSON>", "make", "extraTags", "length", "union"], "sources": ["../../../../src/internal/metric/key.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAKA,IAAAI,MAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,UAAA,GAAAN,OAAA;AACA,IAAAO,aAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,WAAA,GAAAT,uBAAA,CAAAC,OAAA;AAAyC,SAAAD,wBAAAU,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAU,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEzC;AACA,MAAMkB,kBAAkB,GAAG,kBAAkB;AAE7C;AACO,MAAMC,eAAe,GAAAC,OAAA,CAAAD,eAAA,gBAA8BE,MAAM,CAACC,GAAG,CAClEJ,kBAAkB,CACU;AAE9B,MAAMK,iBAAiB,GAAG;EACxB;EACAC,KAAK,EAAGC,CAAQ,IAAKA;CACtB;AAED,MAAMC,gBAAgB,gBAAGtC,GAAG,CAACuC,cAAc,CAACpC,KAAK,CAACqC,MAAM,CAAC;AAEzD;AACA,MAAMC,aAAa;EAGNC,IAAA;EACAC,OAAA;EACAC,WAAA;EACAC,IAAA;EALF,CAACd,eAAe,IAAII,iBAAiB;EAC9CW,YACWJ,IAAY,EACZC,OAAa,EACbC,WAAkC,EAClCC,IAAA,GAA+C,EAAE;IAHjD,KAAAH,IAAI,GAAJA,IAAI;IACJ,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IAEb,IAAI,CAACE,KAAK,GAAG,IAAAC,cAAI,EACf3C,IAAI,CAAC4C,MAAM,CAAC,IAAI,CAACP,IAAI,GAAG,IAAI,CAACE,WAAW,CAAC,EACzCvC,IAAI,CAAC6C,OAAO,CAAC7C,IAAI,CAAC8C,IAAI,CAAC,IAAI,CAACR,OAAO,CAAC,CAAC,EACrCtC,IAAI,CAAC6C,OAAO,CAAC7C,IAAI,CAAC+C,KAAK,CAAC,IAAI,CAACP,IAAI,CAAC,CAAC,CACpC;EACH;EACSE,KAAK;EACd,CAAC1C,IAAI,CAACgD,MAAM,IAAC;IACX,OAAO,IAAI,CAACN,KAAK;EACnB;EACA,CAAC5C,KAAK,CAACkD,MAAM,EAAEC,CAAU;IACvB,OAAOC,WAAW,CAACD,CAAC,CAAC,IACnB,IAAI,CAACZ,IAAI,KAAKY,CAAC,CAACZ,IAAI,IACpBvC,KAAK,CAACqC,MAAM,CAAC,IAAI,CAACG,OAAO,EAAEW,CAAC,CAACX,OAAO,CAAC,IACrCxC,KAAK,CAACqC,MAAM,CAAC,IAAI,CAACI,WAAW,EAAEU,CAAC,CAACV,WAAW,CAAC,IAC7CN,gBAAgB,CAAC,IAAI,CAACO,IAAI,EAAES,CAAC,CAACT,IAAI,CAAC;EACvC;EACAG,IAAIA,CAAA;IACF,OAAO,IAAAQ,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AACO,MAAMF,WAAW,GAAID,CAAU,IACpC,IAAAI,sBAAW,EAACJ,CAAC,EAAEvB,eAAe,CAAC;AAEjC;AAAAC,OAAA,CAAAuB,WAAA,GAAAA,WAAA;AACO,MAAMI,OAAO,GAWhBA,CAACjB,IAAY,EAAEkB,OAAO,KACxB,IAAInB,aAAa,CACfC,IAAI,EACJjC,aAAa,CAACkD,OAAO,CAACC,OAAc,CAAC,EACrCtD,MAAM,CAACuD,YAAY,CAACD,OAAO,EAAEhB,WAAW,CAAC,CAC1C;AAEH;AAAAZ,OAAA,CAAA2B,OAAA,GAAAA,OAAA;AACO,MAAMG,SAAS,GAAGA,CAACpB,IAAY,EAAEkB,OAGvC,KACC,IAAInB,aAAa,CAACC,IAAI,EAAEjC,aAAa,CAACqD,SAAS,CAACF,OAAO,CAAC,EAAEtD,MAAM,CAACuD,YAAY,CAACD,OAAO,EAAEhB,WAAW,CAAC,CAAC;AAEtG;AAAAZ,OAAA,CAAA8B,SAAA,GAAAA,SAAA;AACO,MAAMC,KAAK,GASdA,CAACrB,IAAI,EAAEkB,OAAO,KAChB,IAAInB,aAAa,CACfC,IAAI,EACJjC,aAAa,CAACsD,KAAK,CAACH,OAAc,CAAC,EACnCtD,MAAM,CAACuD,YAAY,CAACD,OAAO,EAAEhB,WAAW,CAAC,CAC1C;AAEH;AAAAZ,OAAA,CAAA+B,KAAA,GAAAA,KAAA;AACO,MAAMC,SAAS,GAAGA,CACvBtB,IAAY,EACZuB,UAA6C,EAC7CrB,WAAoB,KAEpB,IAAIH,aAAa,CACfC,IAAI,EACJjC,aAAa,CAACuD,SAAS,CAACC,UAAU,CAAC,EACnC3D,MAAM,CAACuD,YAAY,CAACjB,WAAW,CAAC,CACjC;AAEH;AAAAZ,OAAA,CAAAgC,SAAA,GAAAA,SAAA;AACO,MAAME,OAAO,GAClBN,OAOC,IAED,IAAInB,aAAa,CACfmB,OAAO,CAAClB,IAAI,EACZjC,aAAa,CAACyD,OAAO,CAACN,OAAO,CAAC,EAC9BtD,MAAM,CAACuD,YAAY,CAACD,OAAO,CAAChB,WAAW,CAAC,CACzC;AAEH;AAAAZ,OAAA,CAAAkC,OAAA,GAAAA,OAAA;AACO,MAAMC,MAAM,GAAAnC,OAAA,CAAAmC,MAAA,gBAAG,IAAAC,cAAI,EAYxB,CAAC,EAAE,CAACC,IAAI,EAAEC,GAAG,EAAEC,KAAK,KAAKC,gBAAgB,CAACH,IAAI,EAAE,CAAC3D,WAAW,CAAC+D,IAAI,CAACH,GAAG,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;AAElF;AACO,MAAMC,gBAAgB,GAAAxC,OAAA,CAAAwC,gBAAA,gBAAG,IAAAJ,cAAI,EAUlC,CAAC,EAAE,CAACC,IAAI,EAAEK,SAAS,KACnBA,SAAS,CAACC,MAAM,KAAK,CAAC,GAClBN,IAAI,GACJ,IAAI5B,aAAa,CAAC4B,IAAI,CAAC3B,IAAI,EAAE2B,IAAI,CAAC1B,OAAO,EAAE0B,IAAI,CAACzB,WAAW,EAAE5C,GAAG,CAAC4E,KAAK,CAACP,IAAI,CAACxB,IAAI,EAAE6B,SAAS,CAAC,CAAC,CAAC", "ignoreList": []}