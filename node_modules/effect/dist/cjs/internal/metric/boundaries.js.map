{"version": 3, "file": "boundaries.js", "names": ["Arr", "_interopRequireWildcard", "require", "Chunk", "Equal", "_Function", "Hash", "_Pipeable", "_Predicate", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "MetricBoundariesSymbolKey", "MetricBoundariesTypeId", "exports", "Symbol", "for", "MetricBoundariesImpl", "values", "constructor", "_hash", "pipe", "string", "combine", "array", "symbol", "u", "isMetricBoundaries", "equals", "pipeArguments", "arguments", "hasProperty", "fromIterable", "iterable", "appendAll", "of", "Number", "POSITIVE_INFINITY", "dedupe", "linear", "options", "<PERSON><PERSON>y", "count", "start", "width", "unsafeFromArray", "exponential", "Math", "pow", "factor"], "sources": ["../../../../src/internal/metric/boundaries.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,IAAA,GAAAL,uBAAA,CAAAC,OAAA;AAEA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,UAAA,GAAAN,OAAA;AAAgD,SAAAD,wBAAAQ,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,CAAAQ,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEhD;AACA,MAAMkB,yBAAyB,GAAG,yBAAyB;AAE3D;AACO,MAAMC,sBAAsB,GAAAC,OAAA,CAAAD,sBAAA,gBAA4CE,MAAM,CAACC,GAAG,CACvFJ,yBAAyB,CACiB;AAE5C;AACA,MAAMK,oBAAoB;EAEHC,MAAA;EADZ,CAACL,sBAAsB,IAA6CA,sBAAsB;EACnGM,YAAqBD,MAA6B;IAA7B,KAAAA,MAAM,GAANA,MAAM;IACzB,IAAI,CAACE,KAAK,GAAG,IAAAC,cAAI,EACf/B,IAAI,CAACgC,MAAM,CAACV,yBAAyB,CAAC,EACtCtB,IAAI,CAACiC,OAAO,CAACjC,IAAI,CAACkC,KAAK,CAAC,IAAI,CAACN,MAAM,CAAC,CAAC,CACtC;EACH;EACSE,KAAK;EACd,CAAC9B,IAAI,CAACmC,MAAM,IAAC;IACX,OAAO,IAAI,CAACL,KAAK;EACnB;EACA,CAAChC,KAAK,CAACqC,MAAM,EAAEC,CAAU;IACvB,OAAOC,kBAAkB,CAACD,CAAC,CAAC,IAAItC,KAAK,CAACwC,MAAM,CAAC,IAAI,CAACV,MAAM,EAAEQ,CAAC,CAACR,MAAM,CAAC;EACrE;EACAG,IAAIA,CAAA;IACF,OAAO,IAAAQ,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AACO,MAAMH,kBAAkB,GAAID,CAAU,IAC3C,IAAAK,sBAAW,EAACL,CAAC,EAAEb,sBAAsB,CAAC;AAExC;AAAAC,OAAA,CAAAa,kBAAA,GAAAA,kBAAA;AACO,MAAMK,YAAY,GAAIC,QAA0B,IAAuC;EAC5F,MAAMf,MAAM,GAAG,IAAAG,cAAI,EACjBY,QAAQ,EACRjD,GAAG,CAACkD,SAAS,CAAC/C,KAAK,CAACgD,EAAE,CAACC,MAAM,CAACC,iBAAiB,CAAC,CAAC,EACjDrD,GAAG,CAACsD,MAAM,CACX;EACD,OAAO,IAAIrB,oBAAoB,CAACC,MAAM,CAAC;AACzC,CAAC;AAED;AAAAJ,OAAA,CAAAkB,YAAA,GAAAA,YAAA;AACO,MAAMO,MAAM,GAAIC,OAItB,IACC,IAAAnB,cAAI,EACFrC,GAAG,CAACyD,MAAM,CAACD,OAAO,CAACE,KAAK,GAAG,CAAC,EAAG1C,CAAC,IAAKwC,OAAO,CAACG,KAAK,GAAG3C,CAAC,GAAGwC,OAAO,CAACI,KAAK,CAAC,EACvEzD,KAAK,CAAC0D,eAAe,EACrBb,YAAY,CACb;AAEH;AAAAlB,OAAA,CAAAyB,MAAA,GAAAA,MAAA;AACO,MAAMO,WAAW,GAAIN,OAI3B,IACC,IAAAnB,cAAI,EACFrC,GAAG,CAACyD,MAAM,CAACD,OAAO,CAACE,KAAK,GAAG,CAAC,EAAG1C,CAAC,IAAKwC,OAAO,CAACG,KAAK,GAAGI,IAAI,CAACC,GAAG,CAACR,OAAO,CAACS,MAAM,EAAEjD,CAAC,CAAC,CAAC,EACjFb,KAAK,CAAC0D,eAAe,EACrBb,YAAY,CACb;AAAAlB,OAAA,CAAAgC,WAAA,GAAAA,WAAA", "ignoreList": []}