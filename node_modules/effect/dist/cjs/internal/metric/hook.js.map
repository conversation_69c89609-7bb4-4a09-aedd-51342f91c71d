{"version": 3, "file": "hook.js", "names": ["Arr", "_interopRequireWildcard", "require", "Duration", "_Function", "number", "Option", "_Pipeable", "metricState", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "MetricHookSymbolKey", "MetricHookTypeId", "exports", "Symbol", "for", "metricHookVariance", "_In", "_", "_Out", "make", "options", "pipe", "pipeArguments", "arguments", "onModify", "dual", "self", "update", "modify", "input", "onUpdate", "bigint0", "BigInt", "counter", "key", "sum", "keyType", "bigint", "canUpdate", "incremental", "value", "_value", "frequency", "values", "Map", "word", "preregisteredWords", "slotCount", "gauge", "_key", "startAt", "v", "histogram", "bounds", "boundaries", "size", "length", "Uint32Array", "Float32Array", "count", "min", "Number", "MAX_VALUE", "max", "MIN_VALUE", "sort", "Order", "map", "from", "to", "mid", "Math", "floor", "boundary", "getBuckets", "builder", "allocate", "cumulated", "buckets", "summary", "error", "maxAge", "maxSize", "quantiles", "sortedQuantiles", "head", "snapshot", "now", "item", "age", "millis", "greaterThanOrEqualTo", "zero", "lessThanOrEqualTo", "push", "calculateQuantiles", "observe", "timestamp", "target", "Date", "sortedSamples", "sampleCount", "isNonEmptyReadonlyArray", "empty", "tail", "slice", "resolvedHead", "resolveQuantile", "none", "resolved", "of", "for<PERSON>ach", "quantile", "consumed", "rest", "rq", "current", "error_1", "sampleCount_1", "current_1", "consumed_1", "quantile_1", "rest_1", "error_2", "sampleCount_2", "current_2", "consumed_2", "quantile_2", "rest_2", "some", "lastNonEmpty", "headValue", "headNonEmpty", "sameHead", "span", "desired", "allowed<PERSON>rror", "candConsumed", "candError", "abs", "valueToReturn", "isNone", "_tag", "prevError", "Error"], "sources": ["../../../../src/internal/metric/hook.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AAEA,IAAAE,SAAA,GAAAF,OAAA;AAIA,IAAAG,MAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,WAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAyC,SAAAD,wBAAAQ,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,CAAAQ,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEzC;AACA,MAAMkB,mBAAmB,GAAG,mBAAmB;AAE/C;AACO,MAAMC,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,gBAAgCE,MAAM,CAACC,GAAG,CACrEJ,mBAAmB,CACW;AAEhC,MAAMK,kBAAkB,GAAG;EACzB;EACAC,GAAG,EAAGC,CAAU,IAAKA,CAAC;EACtB;EACAC,IAAI,EAAGD,CAAQ,IAAKA;CACrB;AAED;AACO,MAAME,IAAI,GACfC,OAIC,KACmC;EACpC,CAACT,gBAAgB,GAAGI,kBAAkB;EACtCM,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC,CAAC;EACD,GAAGH;CACJ,CAAC;AAEF;AAAAR,OAAA,CAAAO,IAAA,GAAAA,IAAA;AACO,MAAMK,QAAQ,GAAAZ,OAAA,CAAAY,QAAA,gBAAG,IAAAC,cAAI,EAG1B,CAAC,EAAE,CAACC,IAAI,EAAE3B,CAAC,MAAM;EACjB,CAACY,gBAAgB,GAAGI,kBAAkB;EACtCM,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC,CAAC;EACDpB,GAAG,EAAEuB,IAAI,CAACvB,GAAG;EACbwB,MAAM,EAAED,IAAI,CAACC,MAAM;EACnBC,MAAM,EAAGC,KAAK,IAAI;IAChBH,IAAI,CAACE,MAAM,CAACC,KAAK,CAAC;IAClB,OAAO9B,CAAC,CAAC8B,KAAK,CAAC;EACjB;CACD,CAAC,CAAC;AAEH;AACO,MAAMC,QAAQ,GAAAlB,OAAA,CAAAkB,QAAA,gBAAG,IAAAL,cAAI,EAG1B,CAAC,EAAE,CAACC,IAAI,EAAE3B,CAAC,MAAM;EACjB,CAACY,gBAAgB,GAAGI,kBAAkB;EACtCM,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC,CAAC;EACDpB,GAAG,EAAEuB,IAAI,CAACvB,GAAG;EACbwB,MAAM,EAAGE,KAAK,IAAI;IAChBH,IAAI,CAACC,MAAM,CAACE,KAAK,CAAC;IAClB,OAAO9B,CAAC,CAAC8B,KAAK,CAAC;EACjB,CAAC;EACDD,MAAM,EAAEF,IAAI,CAACE;CACd,CAAC,CAAC;AAEH,MAAMG,OAAO,gBAAGC,MAAM,CAAC,CAAC,CAAC;AAEzB;AACO,MAAMC,OAAO,GAClBC,GAAmC,IACC;EACpC,IAAIC,GAAG,GAAMD,GAAG,CAACE,OAAO,CAACC,MAAM,GAAGN,OAAY,GAAG,CAAM;EACvD,MAAMO,SAAS,GAAGJ,GAAG,CAACE,OAAO,CAACG,WAAW,GACrCL,GAAG,CAACE,OAAO,CAACC,MAAM,GACfG,KAAQ,IAAKA,KAAK,IAAIT,OAAO,GAC7BS,KAAQ,IAAKA,KAAK,IAAI,CAAC,GACzBC,MAAS,IAAK,IAAI;EACvB,MAAMd,MAAM,GAAIa,KAAQ,IAAI;IAC1B,IAAIF,SAAS,CAACE,KAAK,CAAC,EAAE;MACpBL,GAAG,GAAIA,GAAW,GAAGK,KAAK;IAC5B;EACF,CAAC;EACD,OAAOrB,IAAI,CAAC;IACVhB,GAAG,EAAEA,CAAA,KAAMb,WAAW,CAAC2C,OAAO,CAACE,GAAa,CAAkD;IAC9FR,MAAM;IACNC,MAAM,EAAED;GACT,CAAC;AACJ,CAAC;AAED;AAAAf,OAAA,CAAAqB,OAAA,GAAAA,OAAA;AACO,MAAMS,SAAS,GAAIR,GAAkC,IAAqC;EAC/F,MAAMS,MAAM,GAAG,IAAIC,GAAG,EAAkB;EACxC,KAAK,MAAMC,IAAI,IAAIX,GAAG,CAACE,OAAO,CAACU,kBAAkB,EAAE;IACjDH,MAAM,CAACvC,GAAG,CAACyC,IAAI,EAAE,CAAC,CAAC;EACrB;EACA,MAAMlB,MAAM,GAAIkB,IAAY,IAAI;IAC9B,MAAME,SAAS,GAAGJ,MAAM,CAACxC,GAAG,CAAC0C,IAAI,CAAC,IAAI,CAAC;IACvCF,MAAM,CAACvC,GAAG,CAACyC,IAAI,EAAEE,SAAS,GAAG,CAAC,CAAC;EACjC,CAAC;EACD,OAAO5B,IAAI,CAAC;IACVhB,GAAG,EAAEA,CAAA,KAAMb,WAAW,CAACoD,SAAS,CAACC,MAAM,CAAC;IACxChB,MAAM;IACNC,MAAM,EAAED;GACT,CAAC;AACJ,CAAC;AAED;AAAAf,OAAA,CAAA8B,SAAA,GAAAA,SAAA;AACO,MAAMM,KAAK,GAGdA,CACFC,IAAkC,EAClCC,OAAU,KACwB;EAClC,IAAIV,KAAK,GAAGU,OAAO;EACnB,OAAO/B,IAAI,CAAC;IACVhB,GAAG,EAAEA,CAAA,KAAMb,WAAW,CAAC0D,KAAK,CAACR,KAAe,CAAgD;IAC5Fb,MAAM,EAAGwB,CAAC,IAAI;MACZX,KAAK,GAAGW,CAAC;IACX,CAAC;IACDvB,MAAM,EAAGuB,CAAC,IAAI;MACZX,KAAK,GAAIA,KAAa,GAAGW,CAAC;IAC5B;GACD,CAAC;AACJ,CAAC;AAED;AAAAvC,OAAA,CAAAoC,KAAA,GAAAA,KAAA;AACO,MAAMI,SAAS,GAAIlB,GAAkC,IAAqC;EAC/F,MAAMmB,MAAM,GAAGnB,GAAG,CAACE,OAAO,CAACkB,UAAU,CAACX,MAAM;EAC5C,MAAMY,IAAI,GAAGF,MAAM,CAACG,MAAM;EAC1B,MAAMb,MAAM,GAAG,IAAIc,WAAW,CAACF,IAAI,GAAG,CAAC,CAAC;EACxC,MAAMD,UAAU,GAAG,IAAII,YAAY,CAACH,IAAI,CAAC;EACzC,IAAII,KAAK,GAAG,CAAC;EACb,IAAIxB,GAAG,GAAG,CAAC;EACX,IAAIyB,GAAG,GAAGC,MAAM,CAACC,SAAS;EAC1B,IAAIC,GAAG,GAAGF,MAAM,CAACG,SAAS;EAE1B,IAAA3C,cAAI,EACFgC,MAAM,EACNvE,GAAG,CAACmF,IAAI,CAAC9E,MAAM,CAAC+E,KAAK,CAAC,EACtBpF,GAAG,CAACqF,GAAG,CAAC,CAACxE,CAAC,EAAEG,CAAC,KAAI;IACfwD,UAAU,CAACxD,CAAC,CAAC,GAAGH,CAAC;EACnB,CAAC,CAAC,CACH;EAED;EACA,MAAMgC,MAAM,GAAIa,KAAa,IAAI;IAC/B,IAAI4B,IAAI,GAAG,CAAC;IACZ,IAAIC,EAAE,GAAGd,IAAI;IACb,OAAOa,IAAI,KAAKC,EAAE,EAAE;MAClB,MAAMC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,CAACC,EAAE,GAAGD,IAAI,IAAI,CAAC,CAAC;MAC9C,MAAMK,QAAQ,GAAGnB,UAAU,CAACgB,GAAG,CAAC;MAChC,IAAI9B,KAAK,IAAIiC,QAAQ,EAAE;QACrBJ,EAAE,GAAGC,GAAG;MACV,CAAC,MAAM;QACLF,IAAI,GAAGE,GAAG;MACZ;MACA;MACA,IAAID,EAAE,KAAKD,IAAI,GAAG,CAAC,EAAE;QACnB,IAAI5B,KAAK,IAAIc,UAAU,CAACc,IAAI,CAAC,EAAE;UAC7BC,EAAE,GAAGD,IAAI;QACX,CAAC,MAAM;UACLA,IAAI,GAAGC,EAAE;QACX;MACF;IACF;IACA1B,MAAM,CAACyB,IAAI,CAAC,GAAGzB,MAAM,CAACyB,IAAI,CAAE,GAAG,CAAC;IAChCT,KAAK,GAAGA,KAAK,GAAG,CAAC;IACjBxB,GAAG,GAAGA,GAAG,GAAGK,KAAK;IACjB,IAAIA,KAAK,GAAGoB,GAAG,EAAE;MACfA,GAAG,GAAGpB,KAAK;IACb;IACA,IAAIA,KAAK,GAAGuB,GAAG,EAAE;MACfA,GAAG,GAAGvB,KAAK;IACb;EACF,CAAC;EAED,MAAMkC,UAAU,GAAGA,CAAA,KAA+C;IAChE,MAAMC,OAAO,GAAqC7F,GAAG,CAAC8F,QAAQ,CAACrB,IAAI,CAAQ;IAC3E,IAAIsB,SAAS,GAAG,CAAC;IACjB,KAAK,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,IAAI,EAAEzD,CAAC,EAAE,EAAE;MAC7B,MAAM2E,QAAQ,GAAGnB,UAAU,CAACxD,CAAC,CAAC;MAC9B,MAAM0C,KAAK,GAAGG,MAAM,CAAC7C,CAAC,CAAC;MACvB+E,SAAS,GAAGA,SAAS,GAAGrC,KAAK;MAC7BmC,OAAO,CAAC7E,CAAC,CAAC,GAAG,CAAC2E,QAAQ,EAAEI,SAAS,CAAC;IACpC;IACA,OAAOF,OAAO;EAChB,CAAC;EAED,OAAOxD,IAAI,CAAC;IACVhB,GAAG,EAAEA,CAAA,KACHb,WAAW,CAAC8D,SAAS,CAAC;MACpB0B,OAAO,EAAEJ,UAAU,EAAE;MACrBf,KAAK;MACLC,GAAG;MACHG,GAAG;MACH5B;KACD,CAAC;IACJR,MAAM;IACNC,MAAM,EAAED;GACT,CAAC;AACJ,CAAC;AAED;AAAAf,OAAA,CAAAwC,SAAA,GAAAA,SAAA;AACO,MAAM2B,OAAO,GAAI7C,GAAgC,IAAmC;EACzF,MAAM;IAAE8C,KAAK;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAS,CAAE,GAAGjD,GAAG,CAACE,OAAO;EACzD,MAAMgD,eAAe,GAAG,IAAA/D,cAAI,EAAC8D,SAAS,EAAErG,GAAG,CAACmF,IAAI,CAAC9E,MAAM,CAAC+E,KAAK,CAAC,CAAC;EAC/D,MAAMvB,MAAM,GAAG7D,GAAG,CAAC8F,QAAQ,CAA4BM,OAAO,CAAC;EAE/D,IAAIG,IAAI,GAAG,CAAC;EACZ,IAAI1B,KAAK,GAAG,CAAC;EACb,IAAIxB,GAAG,GAAG,CAAC;EACX,IAAIyB,GAAG,GAAG,CAAC;EACX,IAAIG,GAAG,GAAG,CAAC;EAEX;EACA,MAAMuB,QAAQ,GAAIC,GAAW,IAA6D;IACxF,MAAMZ,OAAO,GAAkB,EAAE;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI7E,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,KAAKoF,OAAO,GAAG,CAAC,EAAE;MACxB,MAAMM,IAAI,GAAG7C,MAAM,CAAC7C,CAAC,CAAC;MACtB,IAAI0F,IAAI,IAAI,IAAI,EAAE;QAChB,MAAM,CAAChG,CAAC,EAAE2D,CAAC,CAAC,GAAGqC,IAAI;QACnB,MAAMC,GAAG,GAAGxG,QAAQ,CAACyG,MAAM,CAACH,GAAG,GAAG/F,CAAC,CAAC;QACpC,IAAIP,QAAQ,CAAC0G,oBAAoB,CAACF,GAAG,EAAExG,QAAQ,CAAC2G,IAAI,CAAC,IAAI3G,QAAQ,CAAC4G,iBAAiB,CAACJ,GAAG,EAAER,MAAM,CAAC,EAAE;UAChGN,OAAO,CAACmB,IAAI,CAAC3C,CAAC,CAAC;QACjB;MACF;MACArD,CAAC,GAAGA,CAAC,GAAG,CAAC;IACX;IACA,OAAOiG,kBAAkB,CACvBf,KAAK,EACLI,eAAe,EACftG,GAAG,CAACmF,IAAI,CAACU,OAAO,EAAExF,MAAM,CAAC+E,KAAK,CAAC,CAChC;EACH,CAAC;EAED,MAAM8B,OAAO,GAAGA,CAACxD,KAAa,EAAEyD,SAAiB,KAAI;IACnD,IAAIf,OAAO,GAAG,CAAC,EAAE;MACfG,IAAI,GAAGA,IAAI,GAAG,CAAC;MACf,MAAMa,MAAM,GAAGb,IAAI,GAAGH,OAAO;MAC7BvC,MAAM,CAACuD,MAAM,CAAC,GAAG,CAACD,SAAS,EAAEzD,KAAK,CAAU;IAC9C;IAEAoB,GAAG,GAAGD,KAAK,KAAK,CAAC,GAAGnB,KAAK,GAAG+B,IAAI,CAACX,GAAG,CAACA,GAAG,EAAEpB,KAAK,CAAC;IAChDuB,GAAG,GAAGJ,KAAK,KAAK,CAAC,GAAGnB,KAAK,GAAG+B,IAAI,CAACR,GAAG,CAACA,GAAG,EAAEvB,KAAK,CAAC;IAEhDmB,KAAK,GAAGA,KAAK,GAAG,CAAC;IACjBxB,GAAG,GAAGA,GAAG,GAAGK,KAAK;EACnB,CAAC;EAED,OAAOrB,IAAI,CAAC;IACVhB,GAAG,EAAEA,CAAA,KACHb,WAAW,CAACyF,OAAO,CAAC;MAClBC,KAAK;MACLG,SAAS,EAAEG,QAAQ,CAACa,IAAI,CAACZ,GAAG,EAAE,CAAC;MAC/B5B,KAAK;MACLC,GAAG;MACHG,GAAG;MACH5B;KACD,CAAC;IACJR,MAAM,EAAEA,CAAC,CAACa,KAAK,EAAEyD,SAAS,CAAC,KAAKD,OAAO,CAACxD,KAAK,EAAEyD,SAAS,CAAC;IACzDrE,MAAM,EAAEA,CAAC,CAACY,KAAK,EAAEyD,SAAS,CAAC,KAAKD,OAAO,CAACxD,KAAK,EAAEyD,SAAS;GACzD,CAAC;AACJ,CAAC;AAuBD;AAAArF,OAAA,CAAAmE,OAAA,GAAAA,OAAA;AACA,MAAMgB,kBAAkB,GAAGA,CACzBf,KAAa,EACbI,eAAsC,EACtCgB,aAAoC,KACuB;EAC3D;EACA,MAAMC,WAAW,GAAGD,aAAa,CAAC5C,MAAM;EACxC,IAAI,CAAC1E,GAAG,CAACwH,uBAAuB,CAAClB,eAAe,CAAC,EAAE;IACjD,OAAOtG,GAAG,CAACyH,KAAK,EAAE;EACpB;EACA,MAAMlB,IAAI,GAAGD,eAAe,CAAC,CAAC,CAAC;EAC/B,MAAMoB,IAAI,GAAGpB,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC;EACrC,MAAMC,YAAY,GAAGC,eAAe,CAClC3B,KAAK,EACLqB,WAAW,EACXjH,MAAM,CAACwH,IAAI,EAAE,EACb,CAAC,EACDvB,IAAI,EACJe,aAAa,CACd;EACD,MAAMS,QAAQ,GAAG/H,GAAG,CAACgI,EAAE,CAACJ,YAAY,CAAC;EACrCF,IAAI,CAACO,OAAO,CAAEC,QAAQ,IAAI;IACxBH,QAAQ,CAACf,IAAI,CACXa,eAAe,CACb3B,KAAK,EACLqB,WAAW,EACXK,YAAY,CAAClE,KAAK,EAClBkE,YAAY,CAACO,QAAQ,EACrBD,QAAQ,EACRN,YAAY,CAACQ,IAAI,CAClB,CACF;EACH,CAAC,CAAC;EACF,OAAOpI,GAAG,CAACqF,GAAG,CAAC0C,QAAQ,EAAGM,EAAE,IAAK,CAACA,EAAE,CAACH,QAAQ,EAAEG,EAAE,CAAC3E,KAAK,CAAU,CAAC;AACpE,CAAC;AAED;AACA,MAAMmE,eAAe,GAAGA,CACtB3B,KAAa,EACbqB,WAAmB,EACnBe,OAA8B,EAC9BH,QAAgB,EAChBD,QAAgB,EAChBE,IAA2B,KACP;EACpB,IAAIG,OAAO,GAAGrC,KAAK;EACnB,IAAIsC,aAAa,GAAGjB,WAAW;EAC/B,IAAIkB,SAAS,GAAGH,OAAO;EACvB,IAAII,UAAU,GAAGP,QAAQ;EACzB,IAAIQ,UAAU,GAAGT,QAAQ;EACzB,IAAIU,MAAM,GAAGR,IAAI;EACjB,IAAIS,OAAO,GAAG3C,KAAK;EACnB,IAAI4C,aAAa,GAAGvB,WAAW;EAC/B,IAAIwB,SAAS,GAAGT,OAAO;EACvB,IAAIU,UAAU,GAAGb,QAAQ;EACzB,IAAIc,UAAU,GAAGf,QAAQ;EACzB,IAAIgB,MAAM,GAAGd,IAAI;EACjB;EACA,OAAO,CAAC,EAAE;IACR;IACA,IAAI,CAACpI,GAAG,CAACwH,uBAAuB,CAACoB,MAAM,CAAC,EAAE;MACxC,OAAO;QACLV,QAAQ,EAAES,UAAU;QACpBjF,KAAK,EAAEpD,MAAM,CAACwH,IAAI,EAAE;QACpBK,QAAQ,EAAEO,UAAU;QACpBN,IAAI,EAAE;OACP;IACH;IACA;IACA;IACA,IAAIO,UAAU,KAAK,CAAC,EAAE;MACpB,OAAO;QACLT,QAAQ,EAAES,UAAU;QACpBjF,KAAK,EAAEpD,MAAM,CAAC6I,IAAI,CAACnJ,GAAG,CAACoJ,YAAY,CAACR,MAAM,CAAC,CAAC;QAC5CT,QAAQ,EAAEO,UAAU,GAAGE,MAAM,CAAClE,MAAM;QACpC0D,IAAI,EAAE;OACP;IACH;IACA;IACA;IACA,MAAMiB,SAAS,GAAGrJ,GAAG,CAACsJ,YAAY,CAACV,MAAM,CAAC,EAAC;IAC3C,MAAMW,QAAQ,GAAGvJ,GAAG,CAACwJ,IAAI,CAACZ,MAAM,EAAG/H,CAAC,IAAKA,CAAC,KAAKwI,SAAS,CAAC;IACzD;IACA,MAAMI,OAAO,GAAGd,UAAU,GAAGH,aAAa;IAC1C;IACA,MAAMkB,YAAY,GAAInB,OAAO,GAAG,CAAC,GAAIkB,OAAO;IAC5C;IACA;IACA;IACA,MAAME,YAAY,GAAGjB,UAAU,GAAGa,QAAQ,CAAC,CAAC,CAAC,CAAC7E,MAAM;IACpD,MAAMkF,SAAS,GAAGnE,IAAI,CAACoE,GAAG,CAACF,YAAY,GAAGF,OAAO,CAAC;IAClD;IACA,IAAIE,YAAY,GAAGF,OAAO,GAAGC,YAAY,EAAE;MACzCb,OAAO,GAAGN,OAAO;MACjBO,aAAa,GAAGN,aAAa;MAC7BO,SAAS,GAAG/I,GAAG,CAACuG,IAAI,CAACqC,MAAM,CAAC;MAC5BI,UAAU,GAAGW,YAAY;MACzBV,UAAU,GAAGN,UAAU;MACvBO,MAAM,GAAGK,QAAQ,CAAC,CAAC,CAAC;MACpBhB,OAAO,GAAGM,OAAO;MACjBL,aAAa,GAAGM,aAAa;MAC7BL,SAAS,GAAGM,SAAS;MACrBL,UAAU,GAAGM,UAAU;MACvBL,UAAU,GAAGM,UAAU;MACvBL,MAAM,GAAGM,MAAM;MACf;IACF;IACA;IACA,IAAIS,YAAY,GAAGF,OAAO,GAAGC,YAAY,EAAE;MACzC,MAAMI,aAAa,GAAGxJ,MAAM,CAACyJ,MAAM,CAACtB,SAAS,CAAC,GAC1CnI,MAAM,CAAC6I,IAAI,CAACE,SAAS,CAAC,GACtBZ,SAAS;MACb,OAAO;QACLP,QAAQ,EAAES,UAAU;QACpBjF,KAAK,EAAEoG,aAAa;QACpB3B,QAAQ,EAAEO,UAAU;QACpBN,IAAI,EAAEQ;OACP;IACH;IACA;IACA;IACA,QAAQH,SAAS,CAACuB,IAAI;MACpB,KAAK,MAAM;QAAE;UACXnB,OAAO,GAAGN,OAAO;UACjBO,aAAa,GAAGN,aAAa;UAC7BO,SAAS,GAAG/I,GAAG,CAACuG,IAAI,CAACqC,MAAM,CAAC;UAC5BI,UAAU,GAAGW,YAAY;UACzBV,UAAU,GAAGN,UAAU;UACvBO,MAAM,GAAGK,QAAQ,CAAC,CAAC,CAAC;UACpBhB,OAAO,GAAGM,OAAO;UACjBL,aAAa,GAAGM,aAAa;UAC7BL,SAAS,GAAGM,SAAS;UACrBL,UAAU,GAAGM,UAAU;UACvBL,UAAU,GAAGM,UAAU;UACvBL,MAAM,GAAGM,MAAM;UACf;QACF;MACA,KAAK,MAAM;QAAE;UACX,MAAMe,SAAS,GAAGxE,IAAI,CAACoE,GAAG,CAACJ,OAAO,GAAGhB,SAAS,CAAC/E,KAAK,CAAC;UACrD,IAAIkG,SAAS,GAAGK,SAAS,EAAE;YACzBpB,OAAO,GAAGN,OAAO;YACjBO,aAAa,GAAGN,aAAa;YAC7BO,SAAS,GAAG/I,GAAG,CAACuG,IAAI,CAACqC,MAAM,CAAC;YAC5BI,UAAU,GAAGW,YAAY;YACzBV,UAAU,GAAGN,UAAU;YACvBO,MAAM,GAAGK,QAAQ,CAAC,CAAC,CAAC;YACpBhB,OAAO,GAAGM,OAAO;YACjBL,aAAa,GAAGM,aAAa;YAC7BL,SAAS,GAAGM,SAAS;YACrBL,UAAU,GAAGM,UAAU;YACvBL,UAAU,GAAGM,UAAU;YACvBL,MAAM,GAAGM,MAAM;YACf;UACF;UACA,OAAO;YACLhB,QAAQ,EAAES,UAAU;YACpBjF,KAAK,EAAEpD,MAAM,CAAC6I,IAAI,CAACV,SAAS,CAAC/E,KAAK,CAAC;YACnCyE,QAAQ,EAAEO,UAAU;YACpBN,IAAI,EAAEQ;WACP;QACH;IACF;EACF;EACA,MAAM,IAAIsB,KAAK,CACb,yGAAyG,CAC1G;AACH,CAAC", "ignoreList": []}