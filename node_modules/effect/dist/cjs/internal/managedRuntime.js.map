{"version": 3, "file": "managedRuntime.js", "names": ["Effectable", "_interopRequireWildcard", "require", "_Pipeable", "_Predicate", "<PERSON><PERSON>", "core", "fiberRuntime", "internalLayer", "circular", "internalRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "isManagedRuntime", "u", "hasProperty", "TypeId", "exports", "provide", "managed", "effect", "flatMap", "runtimeEffect", "rt", "withFiberRuntime", "fiber", "setFiberRefs", "fiberRefs", "currentRuntimeFlags", "runtimeFlags", "provideContext", "context", "ManagedRuntimeProto", "CommitPrototype", "pipe", "pipeArguments", "arguments", "commit", "make", "layer", "memoMap", "unsafeMakeMemoMap", "scope", "unsafeRunSyncEffect", "scopeMake", "buildFiber", "unsafeForkEffect", "tap", "extend", "toRuntimeWithMemoMap", "self", "cachedRuntime", "scheduler", "currentScheduler", "flatten", "await", "assign", "create", "undefined", "runtime", "unsafeRunPromiseEffect", "Promise", "resolve", "dispose", "disposeEffect", "suspend", "die", "close", "exitVoid", "runFork", "options", "unsafeFork", "runSyncExit", "unsafeRunSyncExitEffect", "unsafeRunSyncExit", "runSync", "unsafeRunSync", "runPromiseExit", "unsafeRunPromiseExitEffect", "unsafeRunPromiseExit", "<PERSON><PERSON><PERSON><PERSON>", "unsafeRunCallback", "defaultRuntime", "runPromise", "unsafeRunPromise"], "sources": ["../../../src/internal/managedRuntime.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,UAAA,GAAAC,uBAAA,CAAAC,OAAA;AAKA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAEA,IAAAG,KAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAEA,IAAAI,IAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,YAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,aAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,QAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,eAAA,GAAAT,uBAAA,CAAAC,OAAA;AAA+C,SAAAD,wBAAAU,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAU,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAO/C;AACO,MAAMkB,gBAAgB,GAAIC,CAAU,IAA8C,IAAAC,sBAAW,EAACD,CAAC,EAAEtB,QAAQ,CAACwB,MAAM,CAAC;AAAAC,OAAA,CAAAJ,gBAAA,GAAAA,gBAAA;AAExH,SAASK,OAAOA,CACdC,OAAkC,EAClCC,MAA8B;EAE9B,OAAO/B,IAAI,CAACgC,OAAO,CACjBF,OAAO,CAACG,aAAa,EACpBC,EAAE,IACDlC,IAAI,CAACmC,gBAAgB,CAAEC,KAAK,IAAI;IAC9BA,KAAK,CAACC,YAAY,CAACH,EAAE,CAACI,SAAS,CAAC;IAChCF,KAAK,CAACG,mBAAmB,GAAGL,EAAE,CAACM,YAAY;IAC3C,OAAOxC,IAAI,CAACyC,cAAc,CAACV,MAAM,EAAEG,EAAE,CAACQ,OAAO,CAAC;EAChD,CAAC,CAAC,CACL;AACH;AAEA,MAAMC,mBAAmB,GAAG;EAC1B,GAAGjD,UAAU,CAACkD,eAAe;EAC7B,CAACzC,QAAQ,CAACwB,MAAM,GAAGxB,QAAQ,CAACwB,MAAM;EAClCkB,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC,CAAC;EACDC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACf,aAAa;EAC3B;CACD;AAED;AACO,MAAMgB,IAAI,GAAGA,CAClBC,KAAgC,EAChCC,OAAuB,KACI;EAC3BA,OAAO,GAAGA,OAAO,IAAIjD,aAAa,CAACkD,iBAAiB,EAAE;EACtD,MAAMC,KAAK,GAAGjD,eAAe,CAACkD,mBAAmB,CAACrD,YAAY,CAACsD,SAAS,EAAE,CAAC;EAC3E,IAAIC,UAAkE;EACtE,MAAMvB,aAAa,GAAGjC,IAAI,CAACmC,gBAAgB,CAA0BC,KAAK,IAAI;IAC5E,IAAI,CAACoB,UAAU,EAAE;MACfA,UAAU,GAAGpD,eAAe,CAACqD,gBAAgB,CAC3CzD,IAAI,CAAC0D,GAAG,CACN3D,KAAK,CAAC4D,MAAM,CACVzD,aAAa,CAAC0D,oBAAoB,CAACV,KAAK,EAAEC,OAAO,CAAC,EAClDE,KAAK,CACN,EACAnB,EAAE,IAAI;QACL2B,IAAI,CAACC,aAAa,GAAG5B,EAAE;MACzB,CAAC,CACF,EACD;QAAEmB,KAAK;QAAEU,SAAS,EAAE3B,KAAK,CAAC4B;MAAgB,CAAE,CAC7C;IACH;IACA,OAAOhE,IAAI,CAACiE,OAAO,CAACT,UAAU,CAACU,KAAK,CAAC;EACvC,CAAC,CAAC;EACF,MAAML,IAAI,GAA8BxC,MAAM,CAAC8C,MAAM,CAAC9C,MAAM,CAAC+C,MAAM,CAACzB,mBAAmB,CAAC,EAAE;IACxFQ,OAAO;IACPE,KAAK;IACLpB,aAAa;IACb6B,aAAa,EAAEO,SAAS;IACxBC,OAAOA,CAAA;MACL,OAAOT,IAAI,CAACC,aAAa,KAAKO,SAAS,GACrCjE,eAAe,CAACmE,sBAAsB,CAACV,IAAI,CAAC5B,aAAa,CAAC,GAC1DuC,OAAO,CAACC,OAAO,CAACZ,IAAI,CAACC,aAAa,CAAC;IACvC,CAAC;IACDY,OAAOA,CAAA;MACL,OAAOtE,eAAe,CAACmE,sBAAsB,CAACV,IAAI,CAACc,aAAa,CAAC;IACnE,CAAC;IACDA,aAAa,EAAE3E,IAAI,CAAC4E,OAAO,CAAC,MAAK;MAC/B;MAAEf,IAA2C,CAAC5B,aAAa,GAAGjC,IAAI,CAAC6E,GAAG,CAAC,yBAAyB,CAAC;MACjGhB,IAAI,CAACC,aAAa,GAAGO,SAAS;MAC9B,OAAOtE,KAAK,CAAC+E,KAAK,CAACjB,IAAI,CAACR,KAAK,EAAErD,IAAI,CAAC+E,QAAQ,CAAC;IAC/C,CAAC,CAAC;IACFC,OAAOA,CAAOjD,MAA8B,EAAEkD,OAAgC;MAC5E,OAAOpB,IAAI,CAACC,aAAa,KAAKO,SAAS,GACrCjE,eAAe,CAACqD,gBAAgB,CAAC5B,OAAO,CAACgC,IAAI,EAAE9B,MAAM,CAAC,EAAEkD,OAAO,CAAC,GAChE7E,eAAe,CAAC8E,UAAU,CAACrB,IAAI,CAACC,aAAa,CAAC,CAAC/B,MAAM,EAAEkD,OAAO,CAAC;IACnE,CAAC;IACDE,WAAWA,CAAOpD,MAA8B;MAC9C,OAAO8B,IAAI,CAACC,aAAa,KAAKO,SAAS,GACrCjE,eAAe,CAACgF,uBAAuB,CAACvD,OAAO,CAACgC,IAAI,EAAE9B,MAAM,CAAC,CAAC,GAC9D3B,eAAe,CAACiF,iBAAiB,CAACxB,IAAI,CAACC,aAAa,CAAC,CAAC/B,MAAM,CAAC;IACjE,CAAC;IACDuD,OAAOA,CAAOvD,MAA8B;MAC1C,OAAO8B,IAAI,CAACC,aAAa,KAAKO,SAAS,GACrCjE,eAAe,CAACkD,mBAAmB,CAACzB,OAAO,CAACgC,IAAI,EAAE9B,MAAM,CAAC,CAAC,GAC1D3B,eAAe,CAACmF,aAAa,CAAC1B,IAAI,CAACC,aAAa,CAAC,CAAC/B,MAAM,CAAC;IAC7D,CAAC;IACDyD,cAAcA,CAAOzD,MAA8B,EAAEkD,OAEpD;MACC,OAAOpB,IAAI,CAACC,aAAa,KAAKO,SAAS,GACrCjE,eAAe,CAACqF,0BAA0B,CAAC5D,OAAO,CAACgC,IAAI,EAAE9B,MAAM,CAAC,EAAEkD,OAAO,CAAC,GAC1E7E,eAAe,CAACsF,oBAAoB,CAAC7B,IAAI,CAACC,aAAa,CAAC,CAAC/B,MAAM,EAAEkD,OAAO,CAAC;IAC7E,CAAC;IACDU,WAAWA,CACT5D,MAA8B,EAC9BkD,OAA2D;MAE3D,OAAOpB,IAAI,CAACC,aAAa,KAAKO,SAAS,GACrCjE,eAAe,CAACwF,iBAAiB,CAACxF,eAAe,CAACyF,cAAc,CAAC,CAAChE,OAAO,CAACgC,IAAI,EAAE9B,MAAM,CAAC,EAAEkD,OAAO,CAAC,GACjG7E,eAAe,CAACwF,iBAAiB,CAAC/B,IAAI,CAACC,aAAa,CAAC,CAAC/B,MAAM,EAAEkD,OAAO,CAAC;IAC1E,CAAC;IACDa,UAAUA,CAAO/D,MAA8B,EAAEkD,OAEhD;MACC,OAAOpB,IAAI,CAACC,aAAa,KAAKO,SAAS,GACrCjE,eAAe,CAACmE,sBAAsB,CAAC1C,OAAO,CAACgC,IAAI,EAAE9B,MAAM,CAAC,EAAEkD,OAAO,CAAC,GACtE7E,eAAe,CAAC2F,gBAAgB,CAAClC,IAAI,CAACC,aAAa,CAAC,CAAC/B,MAAM,EAAEkD,OAAO,CAAC;IACzE;GACD,CAAC;EACF,OAAOpB,IAAI;AACb,CAAC;AAAAjC,OAAA,CAAAqB,IAAA,GAAAA,IAAA", "ignoreList": []}