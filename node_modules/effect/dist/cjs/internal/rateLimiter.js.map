{"version": 3, "file": "rateLimiter.js", "names": ["Duration", "_interopRequireWildcard", "require", "Effect", "FiberRef", "_Function", "_GlobalValue", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "make", "algorithm", "interval", "limit", "fixedWindow", "tokenBucket", "exports", "window", "gen", "millisPerToken", "Math", "ceil", "<PERSON><PERSON><PERSON><PERSON>", "semaphore", "makeSemaphore", "latch", "refill", "sleep", "pipe", "zipRight", "releaseAll", "release", "flatMap", "free", "void", "take", "forever", "forkScoped", "interruptible", "uninterruptibleMask", "restore", "currentCost", "cost", "effect", "globalValue", "Symbol", "for", "unsafeMake", "withCost", "locally"], "sources": ["../../../src/internal/rateLimiter.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,QAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,YAAA,GAAAJ,OAAA;AAA+C,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAI/C;AACO,MAAMkB,IAAI,GAAGA,CAAC;EACnBC,SAAS,GAAG,cAAc;EAC1BC,QAAQ;EACRC;AAAK,CAC2B,KAI9B;EACF,QAAQF,SAAS;IACf,KAAK,cAAc;MAAE;QACnB,OAAOG,WAAW,CAACD,KAAK,EAAED,QAAQ,CAAC;MACrC;IACA,KAAK,cAAc;MAAE;QACnB,OAAOG,WAAW,CAACF,KAAK,EAAED,QAAQ,CAAC;MACrC;EACF;AACF,CAAC;AAAAI,OAAA,CAAAN,IAAA,GAAAA,IAAA;AAED,MAAMK,WAAW,GAAGA,CAACF,KAAa,EAAEI,MAAqB,KAKvD9B,MAAM,CAAC+B,GAAG,CAAC,aAAS;EAClB,MAAMC,cAAc,GAAGC,IAAI,CAACC,IAAI,CAACrC,QAAQ,CAACsC,QAAQ,CAACL,MAAM,CAAC,GAAGJ,KAAK,CAAC;EACnE,MAAMU,SAAS,GAAG,OAAOpC,MAAM,CAACqC,aAAa,CAACX,KAAK,CAAC;EACpD,MAAMY,KAAK,GAAG,OAAOtC,MAAM,CAACqC,aAAa,CAAC,CAAC,CAAC;EAC5C,MAAME,MAAM,GAAwBvC,MAAM,CAACwC,KAAK,CAACR,cAAc,CAAC,CAACS,IAAI,CACnEzC,MAAM,CAAC0C,QAAQ,CAACJ,KAAK,CAACK,UAAU,CAAC,EACjC3C,MAAM,CAAC0C,QAAQ,CAACN,SAAS,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC,EACrC5C,MAAM,CAAC6C,OAAO,CAAEC,IAAI,IAAKA,IAAI,KAAKpB,KAAK,GAAG1B,MAAM,CAAC+C,IAAI,GAAGR,MAAM,CAAC,CAChE;EACD,OAAO,IAAAE,cAAI,EACTH,KAAK,CAACU,IAAI,CAAC,CAAC,CAAC,EACbhD,MAAM,CAAC0C,QAAQ,CAACH,MAAM,CAAC,EACvBvC,MAAM,CAACiD,OAAO,EACdjD,MAAM,CAACkD,UAAU,EACjBlD,MAAM,CAACmD,aAAa,CACrB;EACD,MAAMH,IAAI,GAAGhD,MAAM,CAACoD,mBAAmB,CAAEC,OAAO,IAC9CrD,MAAM,CAAC6C,OAAO,CACZ5C,QAAQ,CAACe,GAAG,CAACsC,WAAW,CAAC,EACxBC,IAAI,IAAKvD,MAAM,CAAC0C,QAAQ,CAACW,OAAO,CAACjB,SAAS,CAACY,IAAI,CAACO,IAAI,CAAC,CAAC,EAAEjB,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,CAC3E,CACF;EACD,OAAQY,MAAM,IAAKxD,MAAM,CAAC0C,QAAQ,CAACM,IAAI,EAAEQ,MAAM,CAAC;AAClD,CAAC,CAAC;AAEJ,MAAM7B,WAAW,GAAGA,CAACD,KAAa,EAAEI,MAAqB,KAKvD9B,MAAM,CAAC+B,GAAG,CAAC,aAAS;EAClB,MAAMK,SAAS,GAAG,OAAOpC,MAAM,CAACqC,aAAa,CAACX,KAAK,CAAC;EACpD,MAAMY,KAAK,GAAG,OAAOtC,MAAM,CAACqC,aAAa,CAAC,CAAC,CAAC;EAC5C,OAAO,IAAAI,cAAI,EACTH,KAAK,CAACU,IAAI,CAAC,CAAC,CAAC,EACbhD,MAAM,CAAC0C,QAAQ,CAAC1C,MAAM,CAACwC,KAAK,CAACV,MAAM,CAAC,CAAC,EACrC9B,MAAM,CAAC0C,QAAQ,CAACJ,KAAK,CAACK,UAAU,CAAC,EACjC3C,MAAM,CAAC0C,QAAQ,CAACN,SAAS,CAACO,UAAU,CAAC,EACrC3C,MAAM,CAACiD,OAAO,EACdjD,MAAM,CAACkD,UAAU,EACjBlD,MAAM,CAACmD,aAAa,CACrB;EACD,MAAMH,IAAI,GAAGhD,MAAM,CAACoD,mBAAmB,CAAEC,OAAO,IAC9CrD,MAAM,CAAC6C,OAAO,CACZ5C,QAAQ,CAACe,GAAG,CAACsC,WAAW,CAAC,EACxBC,IAAI,IAAKvD,MAAM,CAAC0C,QAAQ,CAACW,OAAO,CAACjB,SAAS,CAACY,IAAI,CAACO,IAAI,CAAC,CAAC,EAAEjB,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,CAC3E,CACF;EACD,OAAQY,MAAM,IAAKxD,MAAM,CAAC0C,QAAQ,CAACM,IAAI,EAAEQ,MAAM,CAAC;AAClD,CAAC,CAAC;AAEJ;AACA,MAAMF,WAAW,gBAAG,IAAAG,wBAAW,eAC7BC,MAAM,CAACC,GAAG,CAAC,gCAAgC,CAAC,EAC5C,MAAM1D,QAAQ,CAAC2D,UAAU,CAAC,CAAC,CAAC,CAC7B;AAED;AACO,MAAMC,QAAQ,GAAIN,IAAY,IAAKvD,MAAM,CAAC8D,OAAO,CAACR,WAAW,EAAEC,IAAI,CAAC;AAAA1B,OAAA,CAAAgC,QAAA,GAAAA,QAAA", "ignoreList": []}