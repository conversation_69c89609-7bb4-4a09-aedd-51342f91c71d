{"version": 3, "file": "resource.js", "names": ["_Function", "require", "core", "_interopRequireWildcard", "effectable", "fiberRuntime", "schedule_", "scopedRef", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ResourceSymbolKey", "ResourceTypeId", "exports", "Symbol", "for", "resourceVariance", "_E", "_", "_A", "proto", "CommitPrototype", "commit", "auto", "acquire", "policy", "tap", "manual", "acquireRelease", "pipe", "refresh", "schedule_Effect", "interruptible", "forkDaemon", "interruptFiber", "flatMap", "context", "env", "fromAcquire", "exit", "map", "ref", "resource", "create", "provideContext", "self", "identity", "exitSucceed"], "sources": ["../../../src/internal/resource.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AAIA,IAAAC,IAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAD,uBAAA,CAAAF,OAAA;AACA,IAAAI,YAAA,GAAAF,uBAAA,CAAAF,OAAA;AACA,IAAAK,SAAA,GAAAH,uBAAA,CAAAF,OAAA;AACA,IAAAM,SAAA,GAAAJ,uBAAA,CAAAF,OAAA;AAA2C,SAAAE,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE3C;AACA,MAAMkB,iBAAiB,GAAG,iBAAiB;AAE3C;AACO,MAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,gBAA4BE,MAAM,CAACC,GAAG,CAC/DJ,iBAAiB,CACS;AAE5B,MAAMK,gBAAgB,GAAG;EACvB;EACAC,EAAE,EAAGC,CAAM,IAAKA,CAAC;EACjB;EACAC,EAAE,EAAGD,CAAM,IAAKA;CACjB;AAED;AACA,MAAME,KAAK,GAA0C;EACnD,GAAGhC,UAAU,CAACiC,eAAe;EAC7BC,MAAMA,CAAA;IACJ,OAAOlB,GAAG,CAAC,IAAI,CAAC;EAClB,CAAC;EACD,CAACQ,cAAc,GAAGI;CACnB;AAED;AACO,MAAMO,IAAI,GAAGA,CAClBC,OAA+B,EAC/BC,MAA2C,KAE3CvC,IAAI,CAACwC,GAAG,CAACC,MAAM,CAACH,OAAO,CAAC,EAAGG,MAAM,IAC/BtC,YAAY,CAACuC,cAAc,CACzB,IAAAC,cAAI,EACFC,OAAO,CAACH,MAAM,CAAC,EACfrC,SAAS,CAACyC,eAAe,CAACN,MAAM,CAAC,EACjCvC,IAAI,CAAC8C,aAAa,EAClB3C,YAAY,CAAC4C,UAAU,CACxB,EACD/C,IAAI,CAACgD,cAAc,CACpB,CAAC;AAEN;AAAArB,OAAA,CAAAU,IAAA,GAAAA,IAAA;AACO,MAAMI,MAAM,GACjBH,OAA+B,IAE/BtC,IAAI,CAACiD,OAAO,CAACjD,IAAI,CAACkD,OAAO,EAAK,EAAGC,GAAG,IAClC,IAAAR,cAAI,EACFtC,SAAS,CAAC+C,WAAW,CAACpD,IAAI,CAACqD,IAAI,CAACf,OAAO,CAAC,CAAC,EACzCtC,IAAI,CAACsD,GAAG,CAAEC,GAAG,IAAI;EACf,MAAMC,QAAQ,GAAGlC,MAAM,CAACmC,MAAM,CAACvB,KAAK,CAAC;EACrCsB,QAAQ,CAACnD,SAAS,GAAGkD,GAAG;EACxBC,QAAQ,CAAClB,OAAO,GAAGtC,IAAI,CAAC0D,cAAc,CAACpB,OAAO,EAAEa,GAAG,CAAC;EACpD,OAAOK,QAAQ;AACjB,CAAC,CAAC,CACH,CAAC;AAEN;AAAA7B,OAAA,CAAAc,MAAA,GAAAA,MAAA;AACO,MAAMvB,GAAG,GAAUyC,IAA6B,IACrD3D,IAAI,CAACiD,OAAO,CAAC5C,SAAS,CAACa,GAAG,CAACyC,IAAI,CAACtD,SAAS,CAAC,EAAEuD,kBAAQ,CAAC;AAEvD;AAAAjC,OAAA,CAAAT,GAAA,GAAAA,GAAA;AACO,MAAM0B,OAAO,GAAUe,IAA6B,IACzDtD,SAAS,CAACc,GAAG,CACXwC,IAAI,CAACtD,SAAS,EACdL,IAAI,CAACsD,GAAG,CAACK,IAAI,CAACrB,OAAO,EAAEtC,IAAI,CAAC6D,WAAW,CAAC,CACzC;AAAAlC,OAAA,CAAAiB,OAAA,GAAAA,OAAA", "ignoreList": []}