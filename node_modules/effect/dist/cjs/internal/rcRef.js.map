{"version": 3, "file": "rcRef.js", "names": ["Context", "_interopRequireWildcard", "require", "Duration", "Effectable", "_Function", "Readable", "coreEffect", "core", "circular", "fiberRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TypeId", "exports", "Symbol", "for", "stateEmpty", "_tag", "stateClosed", "variance", "_A", "identity", "_E", "RcRefImpl", "Class", "acquire", "context", "scope", "idleTimeToLive", "state", "semaphore", "unsafeMakeSemaphore", "constructor", "commit", "make", "options", "withFiberRuntime", "fiber", "getFiberRef", "currentContext", "scopeTag", "ref", "decode", "undefined", "as", "addFinalizer", "withPermits", "suspend", "close", "scopeClose", "exitVoid", "void", "self_", "self", "uninterruptibleMask", "restore", "interrupt", "refCount", "interruptFiber", "succeed", "scopeMake", "pipe", "bindTo", "bind", "fiberRefLocally", "add", "map", "value", "tap", "sleep", "interruptible", "zipRight", "ensuring", "sync", "forkIn"], "sources": ["../../../src/internal/rcRef.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AAEA,IAAAE,UAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEA,IAAAG,SAAA,GAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAL,uBAAA,CAAAC,OAAA;AAEA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,IAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,QAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,YAAA,GAAAT,uBAAA,CAAAC,OAAA;AAAiD,SAAAD,wBAAAU,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAU,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEjD;AACO,MAAMkB,MAAM,GAAAC,OAAA,CAAAD,MAAA,gBAAiBE,MAAM,CAACC,GAAG,CAAC,cAAc,CAAiB;AAsB9E,MAAMC,UAAU,GAAiB;EAAEC,IAAI,EAAE;AAAO,CAAE;AAClD,MAAMC,WAAW,GAAiB;EAAED,IAAI,EAAE;AAAQ,CAAE;AAEpD,MAAME,QAAQ,GAAmC;EAC/CC,EAAE,EAAEC,kBAAQ;EACZC,EAAE,EAAED;CACL;AAED,MAAME,SAAgB,SAAQrC,UAAU,CAACsC,KAAwB;EAQpDC,OAAA;EACAC,OAAA;EACAC,KAAA;EACAC,cAAA;EAVF,CAAChB,MAAM,IAAgCO,QAAQ;EAC/C,CAAC/B,QAAQ,CAACwB,MAAM,IAAqBxB,QAAQ,CAACwB,MAAM;EAE7DiB,KAAK,GAAab,UAAU;EACnBc,SAAS,gBAAGvC,QAAQ,CAACwC,mBAAmB,CAAC,CAAC,CAAC;EAEpDC,YACWP,OAAkC,EAClCC,OAA+B,EAC/BC,KAAkB,EAClBC,cAA6C;IAEtD,KAAK,EAAE;IALE,KAAAH,OAAO,GAAPA,OAAO;IACP,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IAGvB,IAAI,CAACvB,GAAG,GAAGA,GAAG,CAAC,IAAI,CAAC;EACtB;EACSA,GAAG;EAEZ4B,MAAMA,CAAA;IACJ,OAAO,IAAI,CAAC5B,GAAG;EACjB;;AAGF;AACO,MAAM6B,IAAI,GAAaC,OAG7B,IACC7C,IAAI,CAAC8C,gBAAgB,CAA6CC,KAAK,IAAI;EACzE,MAAMX,OAAO,GAAGW,KAAK,CAACC,WAAW,CAAChD,IAAI,CAACiD,cAAc,CAAqC;EAC1F,MAAMZ,KAAK,GAAG7C,OAAO,CAACuB,GAAG,CAACqB,OAAO,EAAElC,YAAY,CAACgD,QAAQ,CAAC;EACzD,MAAMC,GAAG,GAAG,IAAIlB,SAAS,CACvBY,OAAO,CAACV,OAAoC,EAC5CC,OAAO,EACPC,KAAK,EACLQ,OAAO,CAACP,cAAc,GAAG3C,QAAQ,CAACyD,MAAM,CAACP,OAAO,CAACP,cAAc,CAAC,GAAGe,SAAS,CAC7E;EACD,OAAOrD,IAAI,CAACsD,EAAE,CACZjB,KAAK,CAACkB,YAAY,CAAC,MACjBJ,GAAG,CAACX,SAAS,CAACgB,WAAW,CAAC,CAAC,CAAC,CAACxD,IAAI,CAACyD,OAAO,CAAC,MAAK;IAC7C,MAAMC,KAAK,GAAGP,GAAG,CAACZ,KAAK,CAACZ,IAAI,KAAK,UAAU,GACvC3B,IAAI,CAAC2D,UAAU,CAACR,GAAG,CAACZ,KAAK,CAACF,KAAK,EAAErC,IAAI,CAAC4D,QAAQ,CAAC,GAC/C5D,IAAI,CAAC6D,IAAI;IACbV,GAAG,CAACZ,KAAK,GAAGX,WAAW;IACvB,OAAO8B,KAAK;EACd,CAAC,CAAC,CAAC,CACJ,EACDP,GAAG,CACJ;AACH,CAAC,CAAC;AAEJ;AAAA5B,OAAA,CAAAqB,IAAA,GAAAA,IAAA;AACO,MAAM7B,GAAG,GACd+C,KAAwB,IACK;EAC7B,MAAMC,IAAI,GAAGD,KAAwB;EACrC,OAAO9D,IAAI,CAACgE,mBAAmB,CAAEC,OAAO,IACtCjE,IAAI,CAACyD,OAAO,CAAC,MAAK;IAChB,QAAQM,IAAI,CAACxB,KAAK,CAACZ,IAAI;MACrB,KAAK,QAAQ;QAAE;UACb,OAAO3B,IAAI,CAACkE,SAAS;QACvB;MACA,KAAK,UAAU;QAAE;UACfH,IAAI,CAACxB,KAAK,CAAC4B,QAAQ,EAAE;UACrB,OAAOJ,IAAI,CAACxB,KAAK,CAACQ,KAAK,GACnB/C,IAAI,CAACsD,EAAE,CAACtD,IAAI,CAACoE,cAAc,CAACL,IAAI,CAACxB,KAAK,CAACQ,KAAK,CAAC,EAAEgB,IAAI,CAACxB,KAAK,CAAC,GAC1DvC,IAAI,CAACqE,OAAO,CAACN,IAAI,CAACxB,KAAK,CAAC;QAC9B;MACA,KAAK,OAAO;QAAE;UACZ,OAAOrC,YAAY,CAACoE,SAAS,EAAE,CAACC,IAAI,CAClCxE,UAAU,CAACyE,MAAM,CAAC,OAAO,CAAC,EAC1BzE,UAAU,CAAC0E,IAAI,CAAC,OAAO,EAAE,CAAC;YAAEpC;UAAK,CAAE,KACjC4B,OAAO,CAACjE,IAAI,CAAC0E,eAAe,CAC1BX,IAAI,CAAC5B,OAAuB,EAC5BnC,IAAI,CAACiD,cAAc,EACnBzD,OAAO,CAACmF,GAAG,CAACZ,IAAI,CAAC3B,OAAO,EAAElC,YAAY,CAACgD,QAAQ,EAAEb,KAAK,CAAC,CACxD,CAAC,CAAC,EACLrC,IAAI,CAAC4E,GAAG,CAAC,CAAC;YAAEvC,KAAK;YAAEwC;UAAK,CAAE,KAAI;YAC5B,MAAMtC,KAAK,GAAsB;cAC/BZ,IAAI,EAAE,UAAU;cAChBkD,KAAK;cACLxC,KAAK;cACLU,KAAK,EAAEM,SAAS;cAChBc,QAAQ,EAAE;aACX;YACDJ,IAAI,CAACxB,KAAK,GAAGA,KAAK;YAClB,OAAOA,KAAK;UACd,CAAC,CAAC,CACH;QACH;IACF;EACF,CAAC,CAAC,CACH,CAACgC,IAAI,CACJR,IAAI,CAACvB,SAAS,CAACgB,WAAW,CAAC,CAAC,CAAC,EAC7BzD,UAAU,CAACyE,MAAM,CAAC,OAAO,CAAC,EAC1BzE,UAAU,CAAC0E,IAAI,CAAC,OAAO,EAAE,MAAMvE,YAAY,CAACgD,QAAQ,CAAC,EACrDlD,IAAI,CAAC8E,GAAG,CAAC,CAAC;IAAEzC,KAAK;IAAEE;EAAK,CAAE,KACxBF,KAAK,CAACkB,YAAY,CAAC,MACjBvD,IAAI,CAACyD,OAAO,CAAC,MAAK;IAChBlB,KAAK,CAAC4B,QAAQ,EAAE;IAChB,IAAI5B,KAAK,CAAC4B,QAAQ,GAAG,CAAC,EAAE;MACtB,OAAOnE,IAAI,CAAC6D,IAAI;IAClB;IACA,IAAIE,IAAI,CAACzB,cAAc,KAAKe,SAAS,EAAE;MACrCU,IAAI,CAACxB,KAAK,GAAGb,UAAU;MACvB,OAAO1B,IAAI,CAAC2D,UAAU,CAACpB,KAAK,CAACF,KAAK,EAAErC,IAAI,CAAC4D,QAAQ,CAAC;IACpD;IACA,OAAO7D,UAAU,CAACgF,KAAK,CAAChB,IAAI,CAACzB,cAAc,CAAC,CAACiC,IAAI,CAC/CvE,IAAI,CAACgF,aAAa,EAClBhF,IAAI,CAACiF,QAAQ,CAACjF,IAAI,CAACyD,OAAO,CAAC,MAAK;MAC9B,IAAIM,IAAI,CAACxB,KAAK,CAACZ,IAAI,KAAK,UAAU,IAAIoC,IAAI,CAACxB,KAAK,CAAC4B,QAAQ,KAAK,CAAC,EAAE;QAC/DJ,IAAI,CAACxB,KAAK,GAAGb,UAAU;QACvB,OAAO1B,IAAI,CAAC2D,UAAU,CAACpB,KAAK,CAACF,KAAK,EAAErC,IAAI,CAAC4D,QAAQ,CAAC;MACpD;MACA,OAAO5D,IAAI,CAAC6D,IAAI;IAClB,CAAC,CAAC,CAAC,EACH3D,YAAY,CAACgF,QAAQ,CAAClF,IAAI,CAACmF,IAAI,CAAC,MAAK;MACnC5C,KAAK,CAACQ,KAAK,GAAGM,SAAS;IACzB,CAAC,CAAC,CAAC,EACHpD,QAAQ,CAACmF,MAAM,CAACrB,IAAI,CAAC1B,KAAK,CAAC,EAC3BrC,IAAI,CAAC8E,GAAG,CAAE/B,KAAK,IAAI;MACjBR,KAAK,CAACQ,KAAK,GAAGA,KAAK;IACrB,CAAC,CAAC,EACFgB,IAAI,CAACvB,SAAS,CAACgB,WAAW,CAAC,CAAC,CAAC,CAC9B;EACH,CAAC,CAAC,CACH,CACF,EACDxD,IAAI,CAAC4E,GAAG,CAAC,CAAC;IAAErC;EAAK,CAAE,KAAKA,KAAK,CAACsC,KAAK,CAAC,CACrC;AACH,CAAC;AAAAtD,OAAA,CAAAR,GAAA,GAAAA,GAAA", "ignoreList": []}