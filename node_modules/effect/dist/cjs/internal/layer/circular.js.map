{"version": 3, "file": "circular.js", "names": ["Context", "_interopRequireWildcard", "require", "_Function", "HashSet", "core", "fiberRuntime", "layer", "runtimeFlags", "runtimeFlagsPatch", "supervisor_", "tracer", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "minimumLogLevel", "level", "scopedDiscard", "fiberRefLocallyScoped", "currentMinimumLogLevel", "exports", "withMinimumLogLevel", "dual", "self", "fiberRefLocally", "<PERSON><PERSON><PERSON>ger", "logger", "fiberRefLocallyScopedWith", "currentLoggers", "add", "addLoggerEffect", "effect", "unwrapEffect", "map", "addLoggerScoped", "unwrapScoped", "<PERSON><PERSON><PERSON><PERSON>", "remove", "<PERSON><PERSON><PERSON><PERSON>", "that", "flatMap", "replaceLoggerEffect", "replaceLoggerScoped", "addSupervisor", "supervisor", "currentSupervisor", "current", "Zip", "enableCooperativeYielding", "withRuntimeFlagsScoped", "enable", "CooperativeYielding", "enableInterruption", "Interruption", "enableOpSupervision", "OpSupervision", "enableRuntimeMetrics", "RuntimeMetrics", "enableWindDown", "WindDown", "disableCooperativeYielding", "disable", "disableInterruption", "disableOpSupervision", "disableRuntimeMetrics", "disableWindDown", "setConfigProvider", "config<PERSON><PERSON><PERSON>", "withConfigProviderScoped", "parentSpan", "span", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "make", "spanTag", "name", "options", "addSpanStackTrace", "scoped", "onEnd", "tap", "makeSpanScoped", "addFinalizer", "exit", "setTracer", "withTracerScoped"], "sources": ["../../../../src/internal/layer/circular.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAH,uBAAA,CAAAC,OAAA;AAOA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,YAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,YAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,iBAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,WAAA,GAAAT,uBAAA,CAAAC,OAAA;AACA,IAAAS,MAAA,GAAAV,uBAAA,CAAAC,OAAA;AAAsC,SAAAD,wBAAAW,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAb,uBAAA,YAAAA,CAAAW,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEtC;AAEA;AACO,MAAMkB,eAAe,GAAIC,KAAwB,IACtDzB,KAAK,CAAC0B,aAAa,CACjB3B,YAAY,CAAC4B,qBAAqB,CAChC5B,YAAY,CAAC6B,sBAAsB,EACnCH,KAAK,CACN,CACF;AAEH;AAAAI,OAAA,CAAAL,eAAA,GAAAA,eAAA;AACO,MAAMM,mBAAmB,GAAAD,OAAA,CAAAC,mBAAA,gBAAG,IAAAC,cAAI,EAGrC,CAAC,EAAE,CAACC,IAAI,EAAEP,KAAK,KACf3B,IAAI,CAACmC,eAAe,CAClBlC,YAAY,CAAC6B,sBAAsB,EACnCH,KAAK,CACN,CAACO,IAAI,CAAC,CAAC;AAEV;AACO,MAAME,SAAS,GAAOC,MAAiC,IAC5DnC,KAAK,CAAC0B,aAAa,CACjB3B,YAAY,CAACqC,yBAAyB,CACpCrC,YAAY,CAACsC,cAAc,EAC3BxC,OAAO,CAACyC,GAAG,CAACH,MAAM,CAAC,CACpB,CACF;AAEH;AAAAN,OAAA,CAAAK,SAAA,GAAAA,SAAA;AACO,MAAMK,eAAe,GAC1BC,MAAsD,IAEtDxC,KAAK,CAACyC,YAAY,CAChB3C,IAAI,CAAC4C,GAAG,CAACF,MAAM,EAAEN,SAAS,CAAC,CAC5B;AAEH;AAAAL,OAAA,CAAAU,eAAA,GAAAA,eAAA;AACO,MAAMI,eAAe,GAC1BH,MAAsD,IAEtDxC,KAAK,CAAC4C,YAAY,CAChB9C,IAAI,CAAC4C,GAAG,CAACF,MAAM,EAAEN,SAAS,CAAC,CAC5B;AAEH;AAAAL,OAAA,CAAAc,eAAA,GAAAA,eAAA;AACO,MAAME,YAAY,GAAOV,MAAiC,IAC/DnC,KAAK,CAAC0B,aAAa,CACjB3B,YAAY,CAACqC,yBAAyB,CACpCrC,YAAY,CAACsC,cAAc,EAC3BxC,OAAO,CAACiD,MAAM,CAACX,MAAM,CAAC,CACvB,CACF;AAEH;AAAAN,OAAA,CAAAgB,YAAA,GAAAA,YAAA;AACO,MAAME,aAAa,GAAAlB,OAAA,CAAAkB,aAAA,gBAAG,IAAAhB,cAAI,EAG/B,CAAC,EAAE,CAACC,IAAI,EAAEgB,IAAI,KAAKhD,KAAK,CAACiD,OAAO,CAACJ,YAAY,CAACb,IAAI,CAAC,EAAE,MAAME,SAAS,CAACc,IAAI,CAAC,CAAC,CAAC;AAE9E;AACO,MAAME,mBAAmB,GAAArB,OAAA,CAAAqB,mBAAA,gBAAG,IAAAnB,cAAI,EAQrC,CAAC,EAAE,CAACC,IAAI,EAAEgB,IAAI,KAAKhD,KAAK,CAACiD,OAAO,CAACJ,YAAY,CAACb,IAAI,CAAC,EAAE,MAAMO,eAAe,CAACS,IAAI,CAAC,CAAC,CAAC;AAEpF;AACO,MAAMG,mBAAmB,GAAAtB,OAAA,CAAAsB,mBAAA,gBAAG,IAAApB,cAAI,EAQrC,CAAC,EAAE,CAACC,IAAI,EAAEgB,IAAI,KAAKhD,KAAK,CAACiD,OAAO,CAACJ,YAAY,CAACb,IAAI,CAAC,EAAE,MAAMW,eAAe,CAACK,IAAI,CAAC,CAAC,CAAC;AAEpF;AACO,MAAMI,aAAa,GAAOC,UAAoC,IACnErD,KAAK,CAAC0B,aAAa,CACjB3B,YAAY,CAACqC,yBAAyB,CACpCrC,YAAY,CAACuD,iBAAiB,EAC7BC,OAAO,IAAK,IAAIpD,WAAW,CAACqD,GAAG,CAACD,OAAO,EAAEF,UAAU,CAAC,CACtD,CACF;AAEH;AAAAxB,OAAA,CAAAuB,aAAA,GAAAA,aAAA;AACO,MAAMK,yBAAyB,GAAA5B,OAAA,CAAA4B,yBAAA,gBAAuBzD,KAAK,CAAC0B,aAAa,cAC9E3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACyD,MAAM,CAAC1D,YAAY,CAAC2D,mBAAmB,CAAC,CAC3D,CACF;AAED;AACO,MAAMC,kBAAkB,GAAAhC,OAAA,CAAAgC,kBAAA,gBAAuB7D,KAAK,CAAC0B,aAAa,cACvE3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACyD,MAAM,CAAC1D,YAAY,CAAC6D,YAAY,CAAC,CACpD,CACF;AAED;AACO,MAAMC,mBAAmB,GAAAlC,OAAA,CAAAkC,mBAAA,gBAAuB/D,KAAK,CAAC0B,aAAa,cACxE3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACyD,MAAM,CAAC1D,YAAY,CAAC+D,aAAa,CAAC,CACrD,CACF;AAED;AACO,MAAMC,oBAAoB,GAAApC,OAAA,CAAAoC,oBAAA,gBAAuBjE,KAAK,CAAC0B,aAAa,cACzE3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACyD,MAAM,CAAC1D,YAAY,CAACiE,cAAc,CAAC,CACtD,CACF;AAED;AACO,MAAMC,cAAc,GAAAtC,OAAA,CAAAsC,cAAA,gBAAuBnE,KAAK,CAAC0B,aAAa,cACnE3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACyD,MAAM,CAAC1D,YAAY,CAACmE,QAAQ,CAAC,CAChD,CACF;AAED;AACO,MAAMC,0BAA0B,GAAAxC,OAAA,CAAAwC,0BAAA,gBAAuBrE,KAAK,CAAC0B,aAAa,cAC/E3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACoE,OAAO,CAACrE,YAAY,CAAC2D,mBAAmB,CAAC,CAC5D,CACF;AAED;AACO,MAAMW,mBAAmB,GAAA1C,OAAA,CAAA0C,mBAAA,gBAAuBvE,KAAK,CAAC0B,aAAa,cACxE3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACoE,OAAO,CAACrE,YAAY,CAAC6D,YAAY,CAAC,CACrD,CACF;AAED;AACO,MAAMU,oBAAoB,GAAA3C,OAAA,CAAA2C,oBAAA,gBAAuBxE,KAAK,CAAC0B,aAAa,cACzE3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACoE,OAAO,CAACrE,YAAY,CAAC+D,aAAa,CAAC,CACtD,CACF;AAED;AACO,MAAMS,qBAAqB,GAAA5C,OAAA,CAAA4C,qBAAA,gBAAuBzE,KAAK,CAAC0B,aAAa,cAC1E3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACoE,OAAO,CAACrE,YAAY,CAACiE,cAAc,CAAC,CACvD,CACF;AAED;AACO,MAAMQ,eAAe,GAAA7C,OAAA,CAAA6C,eAAA,gBAAuB1E,KAAK,CAAC0B,aAAa,cACpE3B,YAAY,CAAC2D,sBAAsB,cACjCxD,iBAAiB,CAACoE,OAAO,CAACrE,YAAY,CAACmE,QAAQ,CAAC,CACjD,CACF;AAED;AACO,MAAMO,iBAAiB,GAAIC,cAA6C,IAC7E5E,KAAK,CAAC0B,aAAa,CAAC3B,YAAY,CAAC8E,wBAAwB,CAACD,cAAc,CAAC,CAAC;AAE5E;AAAA/C,OAAA,CAAA8C,iBAAA,GAAAA,iBAAA;AACO,MAAMG,UAAU,GAAIC,IAAoB,IAC7C/E,KAAK,CAACgF,cAAc,CAACvF,OAAO,CAACwF,IAAI,CAAC7E,MAAM,CAAC8E,OAAO,EAAEH,IAAI,CAAC,CAAC;AAE1D;AAAAlD,OAAA,CAAAiD,UAAA,GAAAA,UAAA;AACO,MAAMC,IAAI,GAAGA,CAClBI,IAAY,EACZC,OAIC,KACiC;EAClCA,OAAO,GAAGhF,MAAM,CAACiF,iBAAiB,CAACD,OAAO,CAAQ;EAClD,OAAOpF,KAAK,CAACsF,MAAM,CACjBlF,MAAM,CAAC8E,OAAO,EACdE,OAAO,EAAEG,KAAK,GACVzF,IAAI,CAAC0F,GAAG,CACRzF,YAAY,CAAC0F,cAAc,CAACN,IAAI,EAAEC,OAAO,CAAC,EACzCL,IAAI,IAAKhF,YAAY,CAAC2F,YAAY,CAAEC,IAAI,IAAKP,OAAO,CAACG,KAAM,CAACR,IAAI,EAAEY,IAAI,CAAC,CAAC,CAC1E,GACC5F,YAAY,CAAC0F,cAAc,CAACN,IAAI,EAAEC,OAAO,CAAC,CAC/C;AACH,CAAC;AAED;AAAAvD,OAAA,CAAAkD,IAAA,GAAAA,IAAA;AACO,MAAMa,SAAS,GAAIxF,MAAqB,IAC7CJ,KAAK,CAAC0B,aAAa,CAAC3B,YAAY,CAAC8F,gBAAgB,CAACzF,MAAM,CAAC,CAAC;AAAAyB,OAAA,CAAA+D,SAAA,GAAAA,SAAA", "ignoreList": []}