{"version": 3, "file": "reloadable.js", "names": ["Context", "_interopRequireWildcard", "require", "_Function", "effect", "core", "fiberRuntime", "layer_", "schedule_", "scopedRef", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ReloadableSymbolKey", "ReloadableTypeId", "exports", "Symbol", "for", "reloadableVariance", "_A", "_", "auto", "tag", "options", "scoped", "reloadableTag", "pipe", "build", "manual", "layer", "map", "unsafeGet", "tap", "reloadable", "acquireRelease", "reload", "ignoreLogged", "schedule_Effect", "schedule", "forkDaemon", "interruptFiber", "autoFromConfig", "context", "flatMap", "env", "scheduleFromConfig", "fromAcquire", "ref", "provideContext", "GenericTag", "key", "reloadFork", "asVoid"], "sources": ["../../../src/internal/reloadable.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAAD,OAAA;AAIA,IAAAE,MAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,YAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,SAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,SAAA,GAAAR,uBAAA,CAAAC,OAAA;AAA2C,SAAAD,wBAAAS,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAS,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAE3C;AACA,MAAMkB,mBAAmB,GAAG,mBAAmB;AAE/C;AACO,MAAMC,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,gBAAgCE,MAAM,CAACC,GAAG,CACrEJ,mBAAmB,CACW;AAEhC,MAAMK,kBAAkB,GAAG;EACzB;EACAC,EAAE,EAAGC,CAAM,IAAKA;CACjB;AAED;AACO,MAAMC,IAAI,GAAGA,CAClBC,GAAsB,EACtBC,OAGC,KAEDhC,MAAM,CAACiC,MAAM,CACXC,aAAa,CAACH,GAAG,CAAC,EAClB,IAAAI,cAAI,EACFnC,MAAM,CAACoC,KAAK,CAACC,MAAM,CAACN,GAAG,EAAE;EAAEO,KAAK,EAAEN,OAAO,CAACM;AAAK,CAAE,CAAC,CAAC,EACnDxC,IAAI,CAACyC,GAAG,CAAC9C,OAAO,CAAC+C,SAAS,CAACN,aAAa,CAACH,GAAG,CAAC,CAAC,CAAC,EAC/CjC,IAAI,CAAC2C,GAAG,CAAEC,UAAU,IAClB3C,YAAY,CAAC4C,cAAc,CACzB,IAAAR,cAAI,EACFO,UAAU,CAACE,MAAM,EACjB/C,MAAM,CAACgD,YAAY,EACnB5C,SAAS,CAAC6C,eAAe,CAACd,OAAO,CAACe,QAAQ,CAAC,EAC3ChD,YAAY,CAACiD,UAAU,CACxB,EACDlD,IAAI,CAACmD,cAAc,CACpB,CACF,CACF,CACF;AAEH;AAAAzB,OAAA,CAAAM,IAAA,GAAAA,IAAA;AACO,MAAMoB,cAAc,GAAGA,CAC5BnB,GAAsB,EACtBC,OAGC,KAEDhC,MAAM,CAACiC,MAAM,CACXC,aAAa,CAACH,GAAG,CAAC,EAClB,IAAAI,cAAI,EACFrC,IAAI,CAACqD,OAAO,EAAM,EAClBrD,IAAI,CAACsD,OAAO,CAAEC,GAAG,IACf,IAAAlB,cAAI,EACFnC,MAAM,CAACoC,KAAK,CAACN,IAAI,CAACC,GAAG,EAAE;EACrBO,KAAK,EAAEN,OAAO,CAACM,KAAK;EACpBS,QAAQ,EAAEf,OAAO,CAACsB,kBAAkB,CAACD,GAAG;CACzC,CAAC,CAAC,EACHvD,IAAI,CAACyC,GAAG,CAAC9C,OAAO,CAAC+C,SAAS,CAACN,aAAa,CAACH,GAAG,CAAC,CAAC,CAAC,CAChD,CACF,CACF,CACF;AAEH;AAAAP,OAAA,CAAA0B,cAAA,GAAAA,cAAA;AACO,MAAMnC,GAAG,GACdgB,GAAsB,IAEtBjC,IAAI,CAACsD,OAAO,CACVlB,aAAa,CAACH,GAAG,CAAC,EACjBW,UAAU,IAAKxC,SAAS,CAACa,GAAG,CAAC2B,UAAU,CAACxC,SAAS,CAAC,CACpD;AAEH;AAAAsB,OAAA,CAAAT,GAAA,GAAAA,GAAA;AACO,MAAMsB,MAAM,GAAGA,CACpBN,GAAsB,EACtBC,OAEC,KAEDhC,MAAM,CAACiC,MAAM,CACXC,aAAa,CAACH,GAAG,CAAC,EAClB,IAAAI,cAAI,EACFrC,IAAI,CAACqD,OAAO,EAAM,EAClBrD,IAAI,CAACsD,OAAO,CAAEC,GAAG,IACf,IAAAlB,cAAI,EACFjC,SAAS,CAACqD,WAAW,CAAC,IAAApB,cAAI,EAACnC,MAAM,CAACoC,KAAK,CAACJ,OAAO,CAACM,KAAK,CAAC,EAAExC,IAAI,CAACyC,GAAG,CAAC9C,OAAO,CAAC+C,SAAS,CAACT,GAAG,CAAC,CAAC,CAAC,CAAC,EAC1FjC,IAAI,CAACyC,GAAG,CAAEiB,GAAG,KAAM;EACjB,CAACjC,gBAAgB,GAAGI,kBAAkB;EACtCzB,SAAS,EAAEsD,GAAG;EACdZ,MAAM,EAAE,IAAAT,cAAI,EACVjC,SAAS,CAACc,GAAG,CAACwC,GAAG,EAAE,IAAArB,cAAI,EAACnC,MAAM,CAACoC,KAAK,CAACJ,OAAO,CAACM,KAAK,CAAC,EAAExC,IAAI,CAACyC,GAAG,CAAC9C,OAAO,CAAC+C,SAAS,CAACT,GAAG,CAAC,CAAC,CAAC,CAAC,EACvFjC,IAAI,CAAC2D,cAAc,CAACJ,GAAG,CAAC;CAE3B,CAAC,CAAC,CACJ,CACF,CACF,CACF;AAEH;AAAA7B,OAAA,CAAAa,MAAA,GAAAA,MAAA;AACO,MAAMH,aAAa,GACxBH,GAAsB,IAC6C;EACnE,OAAOtC,OAAO,CAACiE,UAAU,CAAqD,qBAAqB3B,GAAG,CAAC4B,GAAG,GAAG,CAAC;AAChH,CAAC;AAED;AAAAnC,OAAA,CAAAU,aAAA,GAAAA,aAAA;AACO,MAAMU,MAAM,GACjBb,GAAsB,IAEtBjC,IAAI,CAACsD,OAAO,CACVlB,aAAa,CAACH,GAAG,CAAC,EACjBW,UAAU,IAAKA,UAAU,CAACE,MAAM,CAClC;AAEH;AAAApB,OAAA,CAAAoB,MAAA,GAAAA,MAAA;AACO,MAAMgB,UAAU,GACrB7B,GAAsB,IAEtBjC,IAAI,CAACsD,OAAO,CAAClB,aAAa,CAACH,GAAG,CAAC,EAAGW,UAAU,IAC1C,IAAAP,cAAI,EACFO,UAAU,CAACE,MAAM,EACjB/C,MAAM,CAACgD,YAAY,EACnB9C,YAAY,CAACiD,UAAU,EACvBlD,IAAI,CAAC+D,MAAM,CACZ,CAAC;AAAArC,OAAA,CAAAoC,UAAA,GAAAA,UAAA", "ignoreList": []}