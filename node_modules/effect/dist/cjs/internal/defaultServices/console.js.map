{"version": 3, "file": "console.js", "names": ["Context", "_interopRequireWildcard", "require", "core", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TypeId", "exports", "Symbol", "for", "consoleTag", "GenericTag", "defaultConsole", "assert", "condition", "args", "sync", "console", "clear", "count", "label", "<PERSON><PERSON><PERSON><PERSON>", "debug", "dir", "item", "options", "dirxml", "error", "group", "collapsed", "groupCollapsed", "groupEnd", "info", "log", "table", "tabularData", "properties", "time", "timeEnd", "timeLog", "trace", "warn", "unsafe"], "sources": ["../../../../src/internal/defaultServices/console.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAEA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,IAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAkC,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAElC;AACO,MAAMkB,MAAM,GAAAC,OAAA,CAAAD,MAAA,gBAAmBE,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAmB;AAEpF;AACO,MAAMC,UAAU,GAAAH,OAAA,CAAAG,UAAA,gBAAkD3B,OAAO,CAAC4B,UAAU,CACzF,gBAAgB,CACjB;AAED;AACO,MAAMC,cAAc,GAAAL,OAAA,CAAAK,cAAA,GAAoB;EAC7C,CAACN,MAAM,GAAGA,MAAM;EAChBO,MAAMA,CAACC,SAAS,EAAE,GAAGC,IAAI;IACvB,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACJ,MAAM,CAACC,SAAS,EAAE,GAAGC,IAAI,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EACDG,KAAK,eAAEhC,IAAI,CAAC8B,IAAI,CAAC,MAAK;IACpBC,OAAO,CAACC,KAAK,EAAE;EACjB,CAAC,CAAC;EACFC,KAAKA,CAACC,KAAK;IACT,OAAOlC,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACE,KAAK,CAACC,KAAK,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC;EACDC,UAAUA,CAACD,KAAK;IACd,OAAOlC,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACI,UAAU,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC;EACDE,KAAKA,CAAC,GAAGP,IAAI;IACX,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACK,KAAK,CAAC,GAAGP,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACDQ,GAAGA,CAACC,IAAI,EAAEC,OAAO;IACf,OAAOvC,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACM,GAAG,CAACC,IAAI,EAAEC,OAAO,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC;EACDC,MAAMA,CAAC,GAAGX,IAAI;IACZ,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACS,MAAM,CAAC,GAAGX,IAAI,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC;EACDY,KAAKA,CAAC,GAAGZ,IAAI;IACX,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACU,KAAK,CAAC,GAAGZ,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACDa,KAAKA,CAACH,OAAO;IACX,OAAOA,OAAO,EAAEI,SAAS,GACvB3C,IAAI,CAAC8B,IAAI,CAAC,MAAMC,OAAO,CAACa,cAAc,CAACL,OAAO,EAAEL,KAAK,CAAC,CAAC,GACvDlC,IAAI,CAAC8B,IAAI,CAAC,MAAMC,OAAO,CAACW,KAAK,CAACH,OAAO,EAAEL,KAAK,CAAC,CAAC;EAClD,CAAC;EACDW,QAAQ,eAAE7C,IAAI,CAAC8B,IAAI,CAAC,MAAK;IACvBC,OAAO,CAACc,QAAQ,EAAE;EACpB,CAAC,CAAC;EACFC,IAAIA,CAAC,GAAGjB,IAAI;IACV,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACe,IAAI,CAAC,GAAGjB,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACDkB,GAAGA,CAAC,GAAGlB,IAAI;IACT,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACgB,GAAG,CAAC,GAAGlB,IAAI,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC;EACDmB,KAAKA,CAACC,WAAW,EAAEC,UAAU;IAC3B,OAAOlD,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACiB,KAAK,CAACC,WAAW,EAAEC,UAAU,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC;EACDC,IAAIA,CAACjB,KAAK;IACR,OAAOlC,IAAI,CAAC8B,IAAI,CAAC,MAAMC,OAAO,CAACoB,IAAI,CAACjB,KAAK,CAAC,CAAC;EAC7C,CAAC;EACDkB,OAAOA,CAAClB,KAAK;IACX,OAAOlC,IAAI,CAAC8B,IAAI,CAAC,MAAMC,OAAO,CAACqB,OAAO,CAAClB,KAAK,CAAC,CAAC;EAChD,CAAC;EACDmB,OAAOA,CAACnB,KAAK,EAAE,GAAGL,IAAI;IACpB,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACsB,OAAO,CAACnB,KAAK,EAAE,GAAGL,IAAI,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EACDyB,KAAKA,CAAC,GAAGzB,IAAI;IACX,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACuB,KAAK,CAAC,GAAGzB,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACD0B,IAAIA,CAAC,GAAG1B,IAAI;IACV,OAAO7B,IAAI,CAAC8B,IAAI,CAAC,MAAK;MACpBC,OAAO,CAACwB,IAAI,CAAC,GAAG1B,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACD2B,MAAM,EAAEzB;CACT", "ignoreList": []}