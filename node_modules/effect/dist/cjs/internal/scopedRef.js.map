{"version": 3, "file": "scopedRef.js", "names": ["Context", "_interopRequireWildcard", "require", "_Function", "core", "circular", "effectable", "fiberRuntime", "ref", "synchronized", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ScopedRefSymbolKey", "ScopedRefTypeId", "exports", "Symbol", "for", "scopedRefVariance", "_A", "_", "proto", "CommitPrototype", "commit", "close", "self", "flatMap", "tuple", "exitVoid", "fromAcquire", "acquire", "uninterruptible", "scopeMake", "pipe", "newScope", "mapInputContext", "add", "scopeTag", "onError", "cause", "exitFail", "value", "makeSynchronized", "scopedRef", "create", "addFinalizer", "as", "map", "make", "evaluate", "sync", "dual", "flatten", "modifyEffect", "oldScope", "scopeClose", "zipRight", "exit", "scopeExtend", "exitMatch", "onFailure", "failCause", "onSuccess", "succeed", "void"], "sources": ["../../../src/internal/scopedRef.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,SAAA,GAAAD,OAAA;AAGA,IAAAE,IAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,QAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,UAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,YAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,GAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,YAAA,GAAAR,uBAAA,CAAAC,OAAA;AAAoD,SAAAD,wBAAAS,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAS,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEpD;AACA,MAAMkB,kBAAkB,GAAG,kBAAkB;AAE7C;AACO,MAAMC,eAAe,GAAAC,OAAA,CAAAD,eAAA,gBAA8BE,MAAM,CAACC,GAAG,CAClEJ,kBAAkB,CACU;AAE9B;AACA,MAAMK,iBAAiB,GAAG;EACxB;EACAC,EAAE,EAAGC,CAAM,IAAKA;CACjB;AAED;AACA,MAAMC,KAAK,GAAuC;EAChD,GAAG/B,UAAU,CAACgC,eAAe;EAC7BC,MAAMA,CAAA;IACJ,OAAOjB,GAAG,CAAC,IAAI,CAAC;EAClB,CAAC;EACD,CAACQ,eAAe,GAAGI;CACpB;AAED;AACA,MAAMM,KAAK,GAAOC,IAA4B,IAC5CrC,IAAI,CAACsC,OAAO,CAAClC,GAAG,CAACc,GAAG,CAACmB,IAAI,CAACjC,GAAG,CAAC,EAAGmC,KAAK,IAAKA,KAAK,CAAC,CAAC,CAAC,CAACH,KAAK,CAACpC,IAAI,CAACwC,QAAQ,CAAC,CAAC;AAE3E;AACO,MAAMC,WAAW,GACtBC,OAA+B,IAE/B1C,IAAI,CAAC2C,eAAe,CAClBxC,YAAY,CAACyC,SAAS,EAAE,CAACC,IAAI,CAAC7C,IAAI,CAACsC,OAAO,CAAEQ,QAAQ,IAClDJ,OAAO,CAACG,IAAI,CACV7C,IAAI,CAAC+C,eAAe,CAAqBnD,OAAO,CAACoD,GAAG,CAAC7C,YAAY,CAAC8C,QAAQ,EAAEH,QAAQ,CAAC,CAAC,EACtF9C,IAAI,CAACkD,OAAO,CAAEC,KAAK,IAAKL,QAAQ,CAACV,KAAK,CAACpC,IAAI,CAACoD,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAC7DnD,IAAI,CAACsC,OAAO,CAAEe,KAAK,IACjBpD,QAAQ,CAACqD,gBAAgB,CAAC,CAACR,QAAQ,EAAEO,KAAK,CAAU,CAAC,CAACR,IAAI,CACxD7C,IAAI,CAACsC,OAAO,CAAElC,GAAG,IAAI;EACnB,MAAMmD,SAAS,GAAGjC,MAAM,CAACkC,MAAM,CAACvB,KAAK,CAAC;EACtCsB,SAAS,CAACnD,GAAG,GAAGA,GAAG;EACnB,OAAO,IAAAyC,cAAI,EACT1C,YAAY,CAACsD,YAAY,CAAC,MAAMrB,KAAK,CAACmB,SAAS,CAAC,CAAC,EACjDvD,IAAI,CAAC0D,EAAE,CAACH,SAAS,CAAC,CACnB;AACH,CAAC,CAAC,CACH,CACF,CACF,CACF,CAAC,CACH;AAEH;AAAA5B,OAAA,CAAAc,WAAA,GAAAA,WAAA;AACO,MAAMvB,GAAG,GAAOmB,IAA4B,IACjDrC,IAAI,CAAC2D,GAAG,CAACvD,GAAG,CAACc,GAAG,CAACmB,IAAI,CAACjC,GAAG,CAAC,EAAGmC,KAAK,IAAKA,KAAK,CAAC,CAAC,CAAC,CAAC;AAElD;AAAAZ,OAAA,CAAAT,GAAA,GAAAA,GAAA;AACO,MAAM0C,IAAI,GAAOC,QAAoB,IAC1CpB,WAAW,CAACzC,IAAI,CAAC8D,IAAI,CAACD,QAAQ,CAAC,CAAC;AAElC;AAAAlC,OAAA,CAAAiC,IAAA,GAAAA,IAAA;AACO,MAAMzC,GAAG,GAAAQ,OAAA,CAAAR,GAAA,gBAAG,IAAA4C,cAAI,EAQrB,CAAC,EAAE,CACH1B,IAA4B,EAC5BK,OAA+B,KAE/B1C,IAAI,CAACgE,OAAO,CACV3D,YAAY,CAAC4D,YAAY,CAAC5B,IAAI,CAACjC,GAAG,EAAE,CAAC,CAAC8D,QAAQ,EAAEb,KAAK,CAAC,KACpDrD,IAAI,CAAC2C,eAAe,CAClB3C,IAAI,CAACmE,UAAU,CAACD,QAAQ,EAAElE,IAAI,CAACwC,QAAQ,CAAC,CAACK,IAAI,CAC3C7C,IAAI,CAACoE,QAAQ,CAACjE,YAAY,CAACyC,SAAS,EAAE,CAAC,EACvC5C,IAAI,CAACsC,OAAO,CAAEQ,QAAQ,IACpB9C,IAAI,CAACqE,IAAI,CAAClE,YAAY,CAACmE,WAAW,CAAC5B,OAAO,EAAEI,QAAQ,CAAC,CAAC,CAACD,IAAI,CACzD7C,IAAI,CAACsC,OAAO,CAAE+B,IAAI,IAChBrE,IAAI,CAACuE,SAAS,CAACF,IAAI,EAAE;EACnBG,SAAS,EAAGrB,KAAK,IACfnD,IAAI,CAACmE,UAAU,CAACrB,QAAQ,EAAE9C,IAAI,CAACwC,QAAQ,CAAC,CAACK,IAAI,CAC3C7C,IAAI,CAAC0D,EAAE,CACL,CACE1D,IAAI,CAACyE,SAAS,CAACtB,KAAK,CAA2B,EAC/C,CAACe,QAAQ,EAAEb,KAAK,CAAU,CAClB,CACX,CACF;EACHqB,SAAS,EAAGrB,KAAK,IACfrD,IAAI,CAAC2E,OAAO,CACV,CACE3E,IAAI,CAAC4E,IAA8B,EACnC,CAAC9B,QAAQ,EAAEO,KAAK,CAAU,CAClB;CAEf,CAAC,CACH,CACF,CACF,CACF,CACF,CAAC,CACL,CAAC", "ignoreList": []}