{"version": 3, "file": "runtimeFlagsPatch.js", "names": ["_Function", "require", "BIT_MASK", "BIT_SHIFT", "active", "patch", "exports", "enabled", "make", "empty", "enable", "flag", "disable", "isEmpty", "isActive", "dual", "self", "isEnabled", "isDisabled", "exclude", "both", "that", "either", "and<PERSON><PERSON>", "inverse", "invert", "n"], "sources": ["../../../src/internal/runtimeFlagsPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAIA;AACA,MAAMC,QAAQ,GAAG,IAAI;AAErB;AACA,MAAMC,SAAS,GAAG,IAAI;AAEtB;AACO,MAAMC,MAAM,GAAIC,KAA0C,IAAaA,KAAK,GAAGH,QAAQ;AAE9F;AAAAI,OAAA,CAAAF,MAAA,GAAAA,MAAA;AACO,MAAMG,OAAO,GAAIF,KAA0C,IAAcA,KAAK,IAAIF,SAAS,GAAID,QAAQ;AAE9G;AAAAI,OAAA,CAAAC,OAAA,GAAAA,OAAA;AACO,MAAMC,IAAI,GAAGA,CAACJ,MAAc,EAAEG,OAAe,KACjD,CAACH,MAAM,GAAGF,QAAQ,KAAK,CAAEK,OAAO,GAAGH,MAAM,GAAIF,QAAQ,KAAKC,SAAS,CAAyC;AAE/G;AAAAG,OAAA,CAAAE,IAAA,GAAAA,IAAA;AACO,MAAMC,KAAK,GAAAH,OAAA,CAAAG,KAAA,gBAAGD,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAE/B;AACO,MAAME,MAAM,GAAIC,IAA8B,IAA0CH,IAAI,CAACG,IAAI,EAAEA,IAAI,CAAC;AAE/G;AAAAL,OAAA,CAAAI,MAAA,GAAAA,MAAA;AACO,MAAME,OAAO,GAAID,IAA8B,IAA0CH,IAAI,CAACG,IAAI,EAAE,CAAC,CAAC;AAE7G;AAAAL,OAAA,CAAAM,OAAA,GAAAA,OAAA;AACO,MAAMC,OAAO,GAAIR,KAA0C,IAAcA,KAAK,KAAK,CAAC;AAE3F;AAAAC,OAAA,CAAAO,OAAA,GAAAA,OAAA;AACO,MAAMC,QAAQ,GAAAR,OAAA,CAAAQ,QAAA,gBAAG,IAAAC,cAAI,EAG1B,CAAC,EAAE,CAACC,IAAI,EAAEL,IAAI,KAAK,CAACP,MAAM,CAACY,IAAI,CAAC,GAAGL,IAAI,MAAM,CAAC,CAAC;AAEjD;AACO,MAAMM,SAAS,GAAAX,OAAA,CAAAW,SAAA,gBAAG,IAAAF,cAAI,EAG3B,CAAC,EAAE,CAACC,IAAI,EAAEL,IAAI,KAAK,CAACJ,OAAO,CAACS,IAAI,CAAC,GAAGL,IAAI,MAAM,CAAC,CAAC;AAElD;AACO,MAAMO,UAAU,GAAAZ,OAAA,CAAAY,UAAA,gBAAG,IAAAH,cAAI,EAG5B,CAAC,EAAE,CAACC,IAAI,EAAEL,IAAI,KAAM,CAACP,MAAM,CAACY,IAAI,CAAC,GAAGL,IAAI,MAAM,CAAC,IAAM,CAACJ,OAAO,CAACS,IAAI,CAAC,GAAGL,IAAI,MAAM,CAAE,CAAC;AAErF;AACO,MAAMQ,OAAO,GAAAb,OAAA,CAAAa,OAAA,gBAAG,IAAAJ,cAAI,EAKzB,CAAC,EAAE,CAACC,IAAI,EAAEL,IAAI,KAAKH,IAAI,CAACJ,MAAM,CAACY,IAAI,CAAC,GAAG,CAACL,IAAI,EAAEJ,OAAO,CAACS,IAAI,CAAC,CAAC,CAAC;AAE/D;AACO,MAAMI,IAAI,GAAAd,OAAA,CAAAc,IAAA,gBAAG,IAAAL,cAAI,EAUtB,CAAC,EAAE,CAACC,IAAI,EAAEK,IAAI,KAAKb,IAAI,CAACJ,MAAM,CAACY,IAAI,CAAC,GAAGZ,MAAM,CAACiB,IAAI,CAAC,EAAEd,OAAO,CAACS,IAAI,CAAC,GAAGT,OAAO,CAACc,IAAI,CAAC,CAAC,CAAC;AAEtF;AACO,MAAMC,MAAM,GAAAhB,OAAA,CAAAgB,MAAA,gBAAG,IAAAP,cAAI,EAUxB,CAAC,EAAE,CAACC,IAAI,EAAEK,IAAI,KAAKb,IAAI,CAACJ,MAAM,CAACY,IAAI,CAAC,GAAGZ,MAAM,CAACiB,IAAI,CAAC,EAAEd,OAAO,CAACS,IAAI,CAAC,GAAGT,OAAO,CAACc,IAAI,CAAC,CAAC,CAAC;AAEtF;AACO,MAAME,OAAO,GAAAjB,OAAA,CAAAiB,OAAA,gBAAG,IAAAR,cAAI,EAUzB,CAAC,EAAE,CAACC,IAAI,EAAEK,IAAI,KAAML,IAAI,GAAGK,IAA4C,CAAC;AAE1E;AACO,MAAMG,OAAO,GAAInB,KAA0C,IAChEG,IAAI,CAACD,OAAO,CAACF,KAAK,CAAC,EAAEoB,MAAM,CAACrB,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;AAE7C;AAAAC,OAAA,CAAAkB,OAAA,GAAAA,OAAA;AACO,MAAMC,MAAM,GAAIC,CAAS,IAAc,CAACA,CAAC,KAAK,CAAC,GAAIxB,QAAQ;AAAAI,OAAA,CAAAmB,MAAA,GAAAA,MAAA", "ignoreList": []}