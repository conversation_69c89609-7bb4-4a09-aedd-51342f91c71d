{"version": 3, "file": "pathPatch.js", "names": ["RA", "_interopRequireWildcard", "require", "Either", "_Function", "List", "Option", "config<PERSON><PERSON>r", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "empty", "exports", "_tag", "and<PERSON><PERSON>", "dual", "self", "that", "first", "second", "mapName", "nested", "name", "unnested", "patch", "path", "input", "of", "output", "isCons", "head", "tail", "cons", "map", "prepend", "containsName", "pipe", "contains", "tailNonEmpty", "left", "MissingData", "right"], "sources": ["../../../../src/internal/configProvider/pathPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,MAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,WAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAgD,SAAAD,wBAAAO,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAO,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEhD;AACO,MAAMkB,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAAwB;EACxCE,IAAI,EAAE;CACP;AAED;AACO,MAAMC,OAAO,GAAAF,OAAA,CAAAE,OAAA,gBAAG,IAAAC,cAAI,EAGzB,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,MAAM;EACpBJ,IAAI,EAAE,SAAS;EACfK,KAAK,EAAEF,IAAI;EACXG,MAAM,EAAEF;CACT,CAAC,CAAC;AAEH;AACO,MAAMG,OAAO,GAAAR,OAAA,CAAAQ,OAAA,gBAAG,IAAAL,cAAI,EAGzB,CAAC,EAAE,CAACC,IAAI,EAAEhB,CAAC,KAAKc,OAAO,CAACE,IAAI,EAAE;EAAEH,IAAI,EAAE,SAAS;EAAEb;AAAC,CAAE,CAAC,CAAC;AAExD;AACO,MAAMqB,MAAM,GAAAT,OAAA,CAAAS,MAAA,gBAAG,IAAAN,cAAI,EAGxB,CAAC,EAAE,CAACC,IAAI,EAAEM,IAAI,KAAKR,OAAO,CAACE,IAAI,EAAE;EAAEH,IAAI,EAAE,QAAQ;EAAES;AAAI,CAAE,CAAC,CAAC;AAE7D;AACO,MAAMC,QAAQ,GAAAX,OAAA,CAAAW,QAAA,gBAAG,IAAAR,cAAI,EAG1B,CAAC,EAAE,CAACC,IAAI,EAAEM,IAAI,KAAKR,OAAO,CAACE,IAAI,EAAE;EAAEH,IAAI,EAAE,UAAU;EAAES;AAAI,CAAE,CAAC,CAAC;AAE/D;AACO,MAAME,KAAK,GAAAZ,OAAA,CAAAY,KAAA,gBAAG,IAAAT,cAAI,EAUvB,CAAC,EAAE,CAACU,IAAI,EAAED,KAAK,KAAI;EACnB,IAAIE,KAAK,GAAmCrC,IAAI,CAACsC,EAAE,CAACH,KAAK,CAAC;EAC1D,IAAII,MAAM,GAA0BH,IAAI;EACxC,OAAOpC,IAAI,CAACwC,MAAM,CAACH,KAAK,CAAC,EAAE;IACzB,MAAMF,KAAK,GAAwBE,KAAK,CAACI,IAAI;IAC7C,QAAQN,KAAK,CAACX,IAAI;MAChB,KAAK,OAAO;QAAE;UACZa,KAAK,GAAGA,KAAK,CAACK,IAAI;UAClB;QACF;MACA,KAAK,SAAS;QAAE;UACdL,KAAK,GAAGrC,IAAI,CAAC2C,IAAI,CAACR,KAAK,CAACN,KAAK,EAAE7B,IAAI,CAAC2C,IAAI,CAACR,KAAK,CAACL,MAAM,EAAEO,KAAK,CAACK,IAAI,CAAC,CAAC;UACnE;QACF;MACA,KAAK,SAAS;QAAE;UACdH,MAAM,GAAG5C,EAAE,CAACiD,GAAG,CAACL,MAAM,EAAEJ,KAAK,CAACxB,CAAC,CAAC;UAChC0B,KAAK,GAAGA,KAAK,CAACK,IAAI;UAClB;QACF;MACA,KAAK,QAAQ;QAAE;UACbH,MAAM,GAAG5C,EAAE,CAACkD,OAAO,CAACN,MAAM,EAAEJ,KAAK,CAACF,IAAI,CAAC;UACvCI,KAAK,GAAGA,KAAK,CAACK,IAAI;UAClB;QACF;MACA,KAAK,UAAU;QAAE;UACf,MAAMI,YAAY,GAAG,IAAAC,cAAI,EACvBpD,EAAE,CAAC8C,IAAI,CAACF,MAAM,CAAC,EACftC,MAAM,CAAC+C,QAAQ,CAACb,KAAK,CAACF,IAAI,CAAC,CAC5B;UACD,IAAIa,YAAY,EAAE;YAChBP,MAAM,GAAG5C,EAAE,CAACsD,YAAY,CAACV,MAAkC,CAAC;YAC5DF,KAAK,GAAGA,KAAK,CAACK,IAAI;UACpB,CAAC,MAAM;YACL,OAAO5C,MAAM,CAACoD,IAAI,CAAChD,WAAW,CAACiD,WAAW,CACxCZ,MAAM,EACN,YAAYJ,KAAK,CAACF,IAAI,2CAA2C,CAClE,CAAC;UACJ;UACA;QACF;IACF;EACF;EACA,OAAOnC,MAAM,CAACsD,KAAK,CAACb,MAAM,CAAC;AAC7B,CAAC,CAAC", "ignoreList": []}