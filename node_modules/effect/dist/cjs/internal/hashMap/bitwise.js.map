{"version": 3, "file": "bitwise.js", "names": ["_config", "require", "popcount", "x", "hashFragment", "shift", "h", "MASK", "toBitmap", "fromBitmap", "bitmap", "bit"], "sources": ["../../../../src/internal/hashMap/bitwise.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA;;;;;;;AAOM,SAAUC,QAAQA,CAACC,CAAS;EAChCA,CAAC,IAAKA,CAAC,IAAI,CAAC,GAAI,UAAU;EAC1BA,CAAC,GAAG,CAACA,CAAC,GAAG,UAAU,KAAMA,CAAC,IAAI,CAAC,GAAI,UAAU,CAAC;EAC9CA,CAAC,GAAIA,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,GAAI,UAAU;EAC/BA,CAAC,IAAIA,CAAC,IAAI,CAAC;EACXA,CAAC,IAAIA,CAAC,IAAI,EAAE;EACZ,OAAOA,CAAC,GAAG,IAAI;AACjB;AAEA;AACM,SAAUC,YAAYA,CAACC,KAAa,EAAEC,CAAS;EACnD,OAAQA,CAAC,KAAKD,KAAK,GAAIE,YAAI;AAC7B;AAEA;AACM,SAAUC,QAAQA,CAACL,CAAS;EAChC,OAAO,CAAC,IAAIA,CAAC;AACf;AAEA;AACM,SAAUM,UAAUA,CAACC,MAAc,EAAEC,GAAW;EACpD,OAAOT,QAAQ,CAACQ,MAAM,GAAIC,GAAG,GAAG,CAAE,CAAC;AACrC", "ignoreList": []}