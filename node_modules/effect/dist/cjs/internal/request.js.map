{"version": 3, "file": "request.js", "names": ["_Function", "require", "_Predicate", "completedRequestMap", "_interopRequireWildcard", "core", "_effectable", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "RequestSymbolKey", "RequestTypeId", "exports", "Symbol", "for", "requestVariance", "_E", "_", "_A", "RequestPrototype", "StructuralPrototype", "isRequest", "u", "hasProperty", "of", "args", "assign", "create", "tagged", "tag", "request", "_tag", "Class", "prototype", "TaggedClass", "complete", "dual", "self", "result", "fiberRefGetWith", "currentRequestMap", "map", "sync", "entry", "state", "completed", "deferredUnsafeDone", "completeEffect", "effect", "matchEffect", "onFailure", "error", "exitFail", "onSuccess", "value", "exitSucceed", "fail", "failCause", "cause", "exitFailCause", "succeed", "Listeners", "count", "observers", "Set", "interrupted", "addObserver", "add", "removeObserver", "delete", "increment", "for<PERSON>ach", "decrement", "filterOutCompleted", "requests", "filter"], "sources": ["../../../src/internal/request.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAEA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAGA,IAAAE,mBAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,IAAA,GAAAD,uBAAA,CAAAH,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;AAAqD,SAAAG,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAErD;AACA,MAAMkB,gBAAgB,GAAG,gBAAgB;AAEzC;AACO,MAAMC,aAAa,GAAAC,OAAA,CAAAD,aAAA,gBAA0BE,MAAM,CAACC,GAAG,CAC5DJ,gBAAgB,CACQ;AAE1B,MAAMK,eAAe,GAAG;EACtB;EACAC,EAAE,EAAGC,CAAQ,IAAKA,CAAC;EACnB;EACAC,EAAE,EAAGD,CAAQ,IAAKA;CACnB;AAED,MAAME,gBAAgB,GAAG;EACvB,GAAGC,+BAAmB;EACtB,CAACT,aAAa,GAAGI;CAClB;AAED;AACO,MAAMM,SAAS,GAAIC,CAAU,IAA6C,IAAAC,sBAAW,EAACD,CAAC,EAAEX,aAAa,CAAC;AAE9G;AAAAC,OAAA,CAAAS,SAAA,GAAAA,SAAA;AACO,MAAMG,EAAE,GAAGA,CAAA,KAA4EC,IAAI,IAChGlB,MAAM,CAACmB,MAAM,CAACnB,MAAM,CAACoB,MAAM,CAACR,gBAAgB,CAAC,EAAEM,IAAI,CAAC;AAEtD;AAAAb,OAAA,CAAAY,EAAA,GAAAA,EAAA;AACO,MAAMI,MAAM,GACjBC,GAAc,IAEfJ,IAAI,IAAI;EACP,MAAMK,OAAO,GAAGvB,MAAM,CAACmB,MAAM,CAACnB,MAAM,CAACoB,MAAM,CAACR,gBAAgB,CAAC,EAAEM,IAAI,CAAC;EACpEK,OAAO,CAACC,IAAI,GAAGF,GAAG;EAClB,OAAOC,OAAO;AAChB,CAAC;AAED;AAAAlB,OAAA,CAAAgB,MAAA,GAAAA,MAAA;AACO,MAAMI,KAAK,GAAApB,OAAA,CAAAoB,KAAA,gBAGoC;EACpD,SAASA,KAAKA,CAAYP,IAAS;IACjC,IAAIA,IAAI,EAAE;MACRlB,MAAM,CAACmB,MAAM,CAAC,IAAI,EAAED,IAAI,CAAC;IAC3B;EACF;EACAO,KAAK,CAACC,SAAS,GAAGd,gBAAgB;EAClC,OAAOa,KAAY;AACrB,CAAC,CAAC,CAAE;AAEJ;AACO,MAAME,WAAW,GACtBL,GAAQ,IAIoE;EAC5E,OAAO,MAAMK,WAAY,SAAQF,KAAoB;IAC1CD,IAAI,GAAGF,GAAG;GACb;AACV,CAAC;AAED;AAAAjB,OAAA,CAAAsB,WAAA,GAAAA,WAAA;AACO,MAAMC,QAAQ,GAAAvB,OAAA,CAAAuB,QAAA,gBAAG,IAAAC,cAAI,EAQ1B,CAAC,EAAE,CAACC,IAAI,EAAEC,MAAM,KAChBjD,IAAI,CAACkD,eAAe,CAClBpD,mBAAmB,CAACqD,iBAAiB,EACpCC,GAAG,IACFpD,IAAI,CAACqD,IAAI,CAAC,MAAK;EACb,IAAID,GAAG,CAACvC,GAAG,CAACmC,IAAI,CAAC,EAAE;IACjB,MAAMM,KAAK,GAAGF,GAAG,CAACtC,GAAG,CAACkC,IAAI,CAAE;IAC5B,IAAI,CAACM,KAAK,CAACC,KAAK,CAACC,SAAS,EAAE;MAC1BF,KAAK,CAACC,KAAK,CAACC,SAAS,GAAG,IAAI;MAC5BxD,IAAI,CAACyD,kBAAkB,CAACH,KAAK,CAACL,MAAM,EAAEA,MAAM,CAAC;IAC/C;EACF;AACF,CAAC,CAAC,CACL,CAAC;AAEJ;AACO,MAAMS,cAAc,GAAAnC,OAAA,CAAAmC,cAAA,gBAAG,IAAAX,cAAI,EAQhC,CAAC,EAAE,CAACC,IAAI,EAAEW,MAAM,KAChB3D,IAAI,CAAC4D,WAAW,CAACD,MAAM,EAAE;EACvBE,SAAS,EAAGC,KAAK,IAAKhB,QAAQ,CAACE,IAAI,EAAEhD,IAAI,CAAC+D,QAAQ,CAACD,KAAK,CAAQ,CAAC;EACjEE,SAAS,EAAGC,KAAK,IAAKnB,QAAQ,CAACE,IAAI,EAAEhD,IAAI,CAACkE,WAAW,CAACD,KAAK,CAAQ;CACpE,CAAC,CAAC;AAEL;AACO,MAAME,IAAI,GAAA5C,OAAA,CAAA4C,IAAA,gBAAG,IAAApB,cAAI,EAQtB,CAAC,EAAE,CAACC,IAAI,EAAEc,KAAK,KAAKhB,QAAQ,CAACE,IAAI,EAAEhD,IAAI,CAAC+D,QAAQ,CAACD,KAAK,CAAQ,CAAC,CAAC;AAElE;AACO,MAAMM,SAAS,GAAA7C,OAAA,CAAA6C,SAAA,gBAAG,IAAArB,cAAI,EAQ3B,CAAC,EAAE,CAACC,IAAI,EAAEqB,KAAK,KAAKvB,QAAQ,CAACE,IAAI,EAAEhD,IAAI,CAACsE,aAAa,CAACD,KAAK,CAAQ,CAAC,CAAC;AAEvE;AACO,MAAME,OAAO,GAAAhD,OAAA,CAAAgD,OAAA,gBAAG,IAAAxB,cAAI,EAQzB,CAAC,EAAE,CAACC,IAAI,EAAEiB,KAAK,KAAKnB,QAAQ,CAACE,IAAI,EAAEhD,IAAI,CAACkE,WAAW,CAACD,KAAK,CAAQ,CAAC,CAAC;AAErE;AACM,MAAOO,SAAS;EACpBC,KAAK,GAAG,CAAC;EACTC,SAAS,gBAAiC,IAAIC,GAAG,EAAE;EACnDC,WAAW,GAAG,KAAK;EACnBC,WAAWA,CAACnE,CAA0B;IACpC,IAAI,CAACgE,SAAS,CAACI,GAAG,CAACpE,CAAC,CAAC;EACvB;EACAqE,cAAcA,CAACrE,CAA0B;IACvC,IAAI,CAACgE,SAAS,CAACM,MAAM,CAACtE,CAAC,CAAC;EAC1B;EACAuE,SAASA,CAAA;IACP,IAAI,CAACR,KAAK,EAAE;IACZ,IAAI,CAACC,SAAS,CAACQ,OAAO,CAAExE,CAAC,IAAKA,CAAC,CAAC,IAAI,CAAC+D,KAAK,CAAC,CAAC;EAC9C;EACAU,SAASA,CAAA;IACP,IAAI,CAACV,KAAK,EAAE;IACZ,IAAI,CAACC,SAAS,CAACQ,OAAO,CAAExE,CAAC,IAAKA,CAAC,CAAC,IAAI,CAAC+D,KAAK,CAAC,CAAC;EAC9C;;AAGF;;;AAAAlD,OAAA,CAAAiD,SAAA,GAAAA,SAAA;AAGO,MAAMY,kBAAkB,GAAyCC,QAAkB,IACxFrF,IAAI,CAACkD,eAAe,CAClBpD,mBAAmB,CAACqD,iBAAiB,EACpCC,GAAG,IACFpD,IAAI,CAACuE,OAAO,CACVc,QAAQ,CAACC,MAAM,CAAE7C,OAAO,IAAK,EAAEW,GAAG,CAACtC,GAAG,CAAC2B,OAAO,CAAC,EAAEc,KAAK,CAACC,SAAS,KAAK,IAAI,CAAC,CAAC,CAC5E,CACJ;AAAAjC,OAAA,CAAA6D,kBAAA,GAAAA,kBAAA", "ignoreList": []}