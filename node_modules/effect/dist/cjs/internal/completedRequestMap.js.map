{"version": 3, "file": "completedRequestMap.js", "names": ["_GlobalValue", "require", "_core", "currentRequestMap", "exports", "globalValue", "Symbol", "for", "fiberRefUnsafeMake", "Map"], "sources": ["../../../src/internal/completedRequestMap.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAD,OAAA;AAEA;AACO,MAAME,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,gBAAG,IAAAE,wBAAW,eAC1CC,MAAM,CAACC,GAAG,CAAC,mCAAmC,CAAC,EAC/C,MAAM,IAAAC,wBAAkB,EAAC,IAAIC,GAAG,EAA2B,CAAC,CAC7D", "ignoreList": []}