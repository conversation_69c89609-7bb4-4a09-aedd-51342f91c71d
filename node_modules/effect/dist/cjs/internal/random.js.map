{"version": 3, "file": "random.js", "names": ["Chunk", "_interopRequireWildcard", "require", "Context", "_Function", "Hash", "PCGRandom", "core", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "RandomSymbolKey", "RandomTypeId", "exports", "Symbol", "for", "randomTag", "GenericTag", "RandomImpl", "seed", "PRNG", "constructor", "next", "sync", "number", "nextBoolean", "map", "nextInt", "integer", "Number", "MAX_SAFE_INTEGER", "nextRange", "min", "max", "nextIntBetween", "shuffle", "elements", "shuffle<PERSON>ith", "nextIntBounded", "suspend", "pipe", "Array", "from", "flatMap", "buffer", "numbers", "length", "push", "forEachSequentialDiscard", "k", "swap", "as", "fromIterable", "index1", "index2", "tmp", "make", "hash"], "sources": ["../../../src/internal/random.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,uBAAA,CAAAC,OAAA;AAEA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAEA,IAAAI,SAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,IAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAiC,SAAAD,wBAAAO,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAO,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEjC;AACA,MAAMkB,eAAe,GAAG,eAAe;AAEvC;AACO,MAAMC,YAAY,GAAAC,OAAA,CAAAD,YAAA,gBAAwBE,MAAM,CAACC,GAAG,CACzDJ,eAAe,CACO;AAExB;AACO,MAAMK,SAAS,GAAAH,OAAA,CAAAG,SAAA,gBAA8C7B,OAAO,CAAC8B,UAAU,CAAC,eAAe,CAAC;AACvG;AACA,MAAMC,UAAU;EAKOC,IAAA;EAJZ,CAACP,YAAY,IAAyBA,YAAY;EAElDQ,IAAI;EAEbC,YAAqBF,IAAY;IAAZ,KAAAA,IAAI,GAAJA,IAAI;IACvB,IAAI,CAACC,IAAI,GAAG,IAAI9B,SAAS,CAACA,SAAS,CAAC6B,IAAI,CAAC;EAC3C;EAEA,IAAIG,IAAIA,CAAA;IACN,OAAO/B,IAAI,CAACgC,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACI,MAAM,EAAE,CAAC;EAC5C;EAEA,IAAIC,WAAWA,CAAA;IACb,OAAOlC,IAAI,CAACmC,GAAG,CAAC,IAAI,CAACJ,IAAI,EAAG1B,CAAC,IAAKA,CAAC,GAAG,GAAG,CAAC;EAC5C;EAEA,IAAI+B,OAAOA,CAAA;IACT,OAAOpC,IAAI,CAACgC,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACQ,OAAO,CAACC,MAAM,CAACC,gBAAgB,CAAC,CAAC;EACpE;EAEAC,SAASA,CAACC,GAAW,EAAEC,GAAW;IAChC,OAAO1C,IAAI,CAACmC,GAAG,CAAC,IAAI,CAACJ,IAAI,EAAG1B,CAAC,IAAK,CAACqC,GAAG,GAAGD,GAAG,IAAIpC,CAAC,GAAGoC,GAAG,CAAC;EAC1D;EAEAE,cAAcA,CAACF,GAAW,EAAEC,GAAW;IACrC,OAAO1C,IAAI,CAACgC,IAAI,CAAC,MAAM,IAAI,CAACH,IAAI,CAACQ,OAAO,CAACK,GAAG,GAAGD,GAAG,CAAC,GAAGA,GAAG,CAAC;EAC5D;EAEAG,OAAOA,CAAIC,QAAqB;IAC9B,OAAOC,WAAW,CAACD,QAAQ,EAAGxC,CAAC,IAAK,IAAI,CAACsC,cAAc,CAAC,CAAC,EAAEtC,CAAC,CAAC,CAAC;EAChE;;AAGF,MAAMyC,WAAW,GAAGA,CAClBD,QAAqB,EACrBE,cAAoD,KACnB;EACjC,OAAO/C,IAAI,CAACgD,OAAO,CAAC,MAClB,IAAAC,cAAI,EACFjD,IAAI,CAACgC,IAAI,CAAC,MAAMkB,KAAK,CAACC,IAAI,CAACN,QAAQ,CAAC,CAAC,EACrC7C,IAAI,CAACoD,OAAO,CAAEC,MAAM,IAAI;IACtB,MAAMC,OAAO,GAAkB,EAAE;IACjC,KAAK,IAAI9C,CAAC,GAAG6C,MAAM,CAACE,MAAM,EAAE/C,CAAC,IAAI,CAAC,EAAEA,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE;MAC7C8C,OAAO,CAACE,IAAI,CAAChD,CAAC,CAAC;IACjB;IACA,OAAO,IAAAyC,cAAI,EACTK,OAAO,EACPtD,IAAI,CAACyD,wBAAwB,CAAEpD,CAAC,IAC9B,IAAA4C,cAAI,EACFF,cAAc,CAAC1C,CAAC,CAAC,EACjBL,IAAI,CAACmC,GAAG,CAAEuB,CAAC,IAAKC,IAAI,CAACN,MAAM,EAAEhD,CAAC,GAAG,CAAC,EAAEqD,CAAC,CAAC,CAAC,CACxC,CACF,EACD1D,IAAI,CAAC4D,EAAE,CAACnE,KAAK,CAACoE,YAAY,CAACR,MAAM,CAAC,CAAC,CACpC;EACH,CAAC,CAAC,CACH,CACF;AACH,CAAC;AAED,MAAMM,IAAI,GAAGA,CAAIN,MAAgB,EAAES,MAAc,EAAEC,MAAc,KAAc;EAC7E,MAAMC,GAAG,GAAGX,MAAM,CAACS,MAAM,CAAE;EAC3BT,MAAM,CAACS,MAAM,CAAC,GAAGT,MAAM,CAACU,MAAM,CAAE;EAChCV,MAAM,CAACU,MAAM,CAAC,GAAGC,GAAG;EACpB,OAAOX,MAAM;AACf,CAAC;AAEM,MAAMY,IAAI,GAAOrC,IAAO,IAAoB,IAAID,UAAU,CAAC7B,IAAI,CAACoE,IAAI,CAACtC,IAAI,CAAC,CAAC;AAAAN,OAAA,CAAA2C,IAAA,GAAAA,IAAA", "ignoreList": []}