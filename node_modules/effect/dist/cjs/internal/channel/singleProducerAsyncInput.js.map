{"version": 3, "file": "singleProducerAsyncInput.js", "names": ["Cause", "_interopRequireWildcard", "require", "Deferred", "Effect", "Either", "Exit", "_Function", "Ref", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "OP_STATE_EMPTY", "OP_STATE_EMIT", "OP_STATE_ERROR", "OP_STATE_DONE", "stateEmpty", "notifyProducer", "_tag", "stateEmit", "notifyConsumers", "stateError", "cause", "stateDone", "done", "SingleProducerAsyncInputImpl", "ref", "constructor", "awaitR<PERSON>", "flatten", "modify", "state", "await", "void", "close", "fiberIdWith", "fiberId", "error", "interrupt", "value", "for<PERSON>ach", "deferred", "succeed", "left", "discard", "emit", "element", "flatMap", "make", "notifyConsumer", "slice", "undefined", "right", "length", "Error", "failCause", "take", "take<PERSON><PERSON>", "map", "elem", "fail", "onError", "onElement", "onDone", "zipRight", "matchCause", "onFailure", "onSuccess", "match", "onLeft", "onRight", "pipe", "exports"], "sources": ["../../../../src/internal/channel/singleProducerAsyncInput.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,IAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,GAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAmC,SAAAD,wBAAAQ,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,CAAAQ,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAUnC;AACA,MAAMkB,cAAc,GAAG,OAAgB;AAKvC;AACA,MAAMC,aAAa,GAAG,MAAe;AAKrC;AACA,MAAMC,cAAc,GAAG,OAAgB;AAKvC;AACA,MAAMC,aAAa,GAAG,MAAe;AA6BrC;AACA,MAAMC,UAAU,GAAIC,cAAuC,KAAkC;EAC3FC,IAAI,EAAEN,cAAc;EACpBK;CACD,CAAC;AAEF;AACA,MAAME,SAAS,GACbC,eAAiF,KACrD;EAC5BF,IAAI,EAAEL,aAAa;EACnBO;CACD,CAAC;AAEF;AACA,MAAMC,UAAU,GAASC,KAAuB,KAAgC;EAC9EJ,IAAI,EAAEJ,cAAc;EACpBQ;CACD,CAAC;AAEF;AACA,MAAMC,SAAS,GAAUC,IAAU,KAAiC;EAClEN,IAAI,EAAEH,aAAa;EACnBS;CACD,CAAC;AAEF;AACA,MAAMC,4BAA4B;EAGXC,GAAA;EAArBC,YAAqBD,GAAoC;IAApC,KAAAA,GAAG,GAAHA,GAAG;EACxB;EAEAE,SAASA,CAAA;IACP,OAAOxC,MAAM,CAACyC,OAAO,CACnBrC,GAAG,CAACsC,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IACzBA,KAAK,CAACb,IAAI,KAAKN,cAAc,GAC3B,CAACzB,QAAQ,CAAC6C,KAAK,CAACD,KAAK,CAACd,cAAc,CAAC,EAAEc,KAA+B,CAAC,GACvE,CAAC3C,MAAM,CAAC6C,IAAI,EAAEF,KAAK,CAAC,CAAC,CAC1B;EACH;EAEA,IAAIG,KAAKA,CAAA;IACP,OAAO9C,MAAM,CAAC+C,WAAW,CAAEC,OAAO,IAAK,IAAI,CAACC,KAAK,CAACrD,KAAK,CAACsD,SAAS,CAACF,OAAO,CAAC,CAAC,CAAC;EAC9E;EAEAZ,IAAIA,CAACe,KAAW;IACd,OAAOnD,MAAM,CAACyC,OAAO,CACnBrC,GAAG,CAACsC,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACb,IAAI;QAChB,KAAKN,cAAc;UAAE;YACnB,OAAO,CAACzB,QAAQ,CAAC6C,KAAK,CAACD,KAAK,CAACd,cAAc,CAAC,EAAEc,KAAK,CAAC;UACtD;QACA,KAAKlB,aAAa;UAAE;YAClB,OAAO,CACLzB,MAAM,CAACoD,OAAO,CACZT,KAAK,CAACX,eAAe,EACpBqB,QAAQ,IAAKtD,QAAQ,CAACuD,OAAO,CAACD,QAAQ,EAAEpD,MAAM,CAACsD,IAAI,CAACJ,KAAK,CAAC,CAAC,EAC5D;cAAEK,OAAO,EAAE;YAAI,CAAE,CAClB,EACDrB,SAAS,CAACgB,KAAK,CAA2B,CAC3C;UACH;QACA,KAAKzB,cAAc;UAAE;YACnB,OAAO,CAAC1B,MAAM,CAACkD,SAAS,EAAEP,KAAK,CAAC;UAClC;QACA,KAAKhB,aAAa;UAAE;YAClB,OAAO,CAAC3B,MAAM,CAACkD,SAAS,EAAEP,KAAK,CAAC;UAClC;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEAc,IAAIA,CAACC,OAAa;IAChB,OAAO1D,MAAM,CAAC2D,OAAO,CAAC5D,QAAQ,CAAC6D,IAAI,EAAQ,EAAGP,QAAQ,IACpDrD,MAAM,CAACyC,OAAO,CACZrC,GAAG,CAACsC,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACb,IAAI;QAChB,KAAKN,cAAc;UAAE;YACnB,OAAO,CAACzB,QAAQ,CAAC6C,KAAK,CAACD,KAAK,CAACd,cAAc,CAAC,EAAEc,KAAK,CAAC;UACtD;QACA,KAAKlB,aAAa;UAAE;YAClB,MAAMoC,cAAc,GAAGlB,KAAK,CAACX,eAAe,CAAC,CAAC,CAAC;YAC/C,MAAMA,eAAe,GAAGW,KAAK,CAACX,eAAe,CAAC8B,KAAK,CAAC,CAAC,CAAC;YACtD,IAAID,cAAc,KAAKE,SAAS,EAAE;cAChC,OAAO,CACLhE,QAAQ,CAACuD,OAAO,CAACO,cAAc,EAAE5D,MAAM,CAAC+D,KAAK,CAACN,OAAO,CAAC,CAAC,EACtD1B,eAAe,CAACiC,MAAM,KAAK,CAAC,GAC3BrC,UAAU,CAACyB,QAAQ,CAAC,GACpBtB,SAAS,CAACC,eAAe,CAAC,CAC7B;YACH;YACA,MAAM,IAAIkC,KAAK,CACb,oIAAoI,CACrI;UACH;QACA,KAAKxC,cAAc;UAAE;YACnB,OAAO,CAAC1B,MAAM,CAACkD,SAAS,EAAEP,KAAK,CAAC;UAClC;QACA,KAAKhB,aAAa;UAAE;YAClB,OAAO,CAAC3B,MAAM,CAACkD,SAAS,EAAEP,KAAK,CAAC;UAClC;MACF;IACF,CAAC,CAAC,CACH,CAAC;EACN;EAEAM,KAAKA,CAACf,KAAuB;IAC3B,OAAOlC,MAAM,CAACyC,OAAO,CACnBrC,GAAG,CAACsC,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACb,IAAI;QAChB,KAAKN,cAAc;UAAE;YACnB,OAAO,CAACzB,QAAQ,CAAC6C,KAAK,CAACD,KAAK,CAACd,cAAc,CAAC,EAAEc,KAAK,CAAC;UACtD;QACA,KAAKlB,aAAa;UAAE;YAClB,OAAO,CACLzB,MAAM,CAACoD,OAAO,CACZT,KAAK,CAACX,eAAe,EACpBqB,QAAQ,IAAKtD,QAAQ,CAACoE,SAAS,CAACd,QAAQ,EAAEnB,KAAK,CAAC,EACjD;cAAEsB,OAAO,EAAE;YAAI,CAAE,CAClB,EACDvB,UAAU,CAACC,KAAK,CAA2B,CAC5C;UACH;QACA,KAAKR,cAAc;UAAE;YACnB,OAAO,CAAC1B,MAAM,CAACkD,SAAS,EAAEP,KAAK,CAAC;UAClC;QACA,KAAKhB,aAAa;UAAE;YAClB,OAAO,CAAC3B,MAAM,CAACkD,SAAS,EAAEP,KAAK,CAAC;UAClC;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEA,IAAIyB,IAAIA,CAAA;IACN,OAAO,IAAI,CAACC,QAAQ,CACjBnC,KAAK,IAAKhC,IAAI,CAACiE,SAAS,CAACvE,KAAK,CAAC0E,GAAG,CAACpC,KAAK,EAAEjC,MAAM,CAACsD,IAAI,CAAC,CAAC,EACvDgB,IAAI,IAAKrE,IAAI,CAACoD,OAAO,CAACiB,IAAI,CAA8C,EACxEnC,IAAI,IAAKlC,IAAI,CAACsE,IAAI,CAACvE,MAAM,CAAC+D,KAAK,CAAC5B,IAAI,CAAC,CAAC,CACxC;EACH;EAEAiC,QAAQA,CACNI,OAAuC,EACvCC,SAA+B,EAC/BC,MAA0B;IAE1B,OAAO3E,MAAM,CAAC2D,OAAO,CAAC5D,QAAQ,CAAC6D,IAAI,EAAkC,EAAGP,QAAQ,IAC9ErD,MAAM,CAACyC,OAAO,CACZrC,GAAG,CAACsC,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACb,IAAI;QAChB,KAAKN,cAAc;UAAE;YACnB,OAAO,CACLxB,MAAM,CAAC4E,QAAQ,CACb7E,QAAQ,CAACuD,OAAO,CAACX,KAAK,CAACd,cAAc,EAAE,KAAK,CAAC,CAAC,EAC9C7B,MAAM,CAAC6E,UAAU,CAAC9E,QAAQ,CAAC6C,KAAK,CAACS,QAAQ,CAAC,EAAE;cAC1CyB,SAAS,EAAEL,OAAO;cAClBM,SAAS,EAAE9E,MAAM,CAAC+E,KAAK,CAAC;gBAAEC,MAAM,EAAEN,MAAM;gBAAEO,OAAO,EAAER;cAAS,CAAE;aAC/D,CAAC,CACH,EACD3C,SAAS,CAAC,CAACsB,QAAQ,CAAC,CAAC,CACtB;UACH;QACA,KAAK5B,aAAa;UAAE;YAClB,OAAO,CACLzB,MAAM,CAAC6E,UAAU,CAAC9E,QAAQ,CAAC6C,KAAK,CAACS,QAAQ,CAAC,EAAE;cAC1CyB,SAAS,EAAEL,OAAO;cAClBM,SAAS,EAAE9E,MAAM,CAAC+E,KAAK,CAAC;gBAAEC,MAAM,EAAEN,MAAM;gBAAEO,OAAO,EAAER;cAAS,CAAE;aAC/D,CAAC,EACF3C,SAAS,CAAC,CAAC,GAAGY,KAAK,CAACX,eAAe,EAAEqB,QAAQ,CAAC,CAAC,CAChD;UACH;QACA,KAAK3B,cAAc;UAAE;YACnB,OAAO,CAAC1B,MAAM,CAACsD,OAAO,CAACmB,OAAO,CAAC9B,KAAK,CAACT,KAAK,CAAC,CAAC,EAAES,KAAK,CAAC;UACtD;QACA,KAAKhB,aAAa;UAAE;YAClB,OAAO,CAAC3B,MAAM,CAACsD,OAAO,CAACqB,MAAM,CAAChC,KAAK,CAACP,IAAI,CAAC,CAAC,EAAEO,KAAK,CAAC;UACpD;MACF;IACF,CAAC,CAAC,CACH,CAAC;EACN;;AAGF;AACO,MAAMiB,IAAI,GAAGA,CAAA,KAGlB,IAAAuB,cAAI,EACFpF,QAAQ,CAAC6D,IAAI,EAAQ,EACrB5D,MAAM,CAAC2D,OAAO,CAAEN,QAAQ,IAAKjD,GAAG,CAACwD,IAAI,CAAChC,UAAU,CAACyB,QAAQ,CAA2B,CAAC,CAAC,EACtFrD,MAAM,CAACsE,GAAG,CAAEhC,GAAG,IAAK,IAAID,4BAA4B,CAACC,GAAG,CAAC,CAAC,CAC3D;AAAA8C,OAAA,CAAAxB,IAAA,GAAAA,IAAA", "ignoreList": []}