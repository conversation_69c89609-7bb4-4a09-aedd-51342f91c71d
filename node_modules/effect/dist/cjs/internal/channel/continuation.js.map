{"version": 3, "file": "continuation.js", "names": ["Exit", "_interopRequireWildcard", "require", "OpCodes", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ContinuationTypeId", "exports", "Symbol", "for", "continuation<PERSON><PERSON><PERSON>", "_Env", "_", "_InErr", "_InElem", "_InDone", "_OutErr", "_OutDone", "_OutErr2", "_OutElem", "_OutDone2", "ContinuationKImpl", "onSuccess", "onHalt", "_tag", "OP_CONTINUATION_K", "constructor", "onExit", "exit", "isFailure", "cause", "value", "ContinuationFinalizerImpl", "finalizer", "OP_CONTINUATION_FINALIZER"], "sources": ["../../../../src/internal/channel/continuation.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA,IAAAA,IAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAqD,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAErD;AACO,MAAMkB,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,gBAAGE,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC;AAuH1E,MAAMC,oBAAoB,GAAG;EAC3B;EACAC,IAAI,EAAGC,CAAQ,IAAKA,CAAC;EACrB;EACAC,MAAM,EAAGD,CAAU,IAAKA,CAAC;EACzB;EACAE,OAAO,EAAGF,CAAU,IAAKA,CAAC;EAC1B;EACAG,OAAO,EAAGH,CAAU,IAAKA,CAAC;EAC1B;EACAI,OAAO,EAAGJ,CAAQ,IAAKA,CAAC;EACxB;EACAK,QAAQ,EAAGL,CAAQ,IAAKA,CAAC;EACzB;EACAM,QAAQ,EAAGN,CAAQ,IAAKA,CAAC;EACzB;EACAO,QAAQ,EAAGP,CAAQ,IAAKA,CAAC;EACzB;EACAQ,SAAS,EAAGR,CAAQ,IAAKA;CAC1B;AAED;AACM,MAAOS,iBAAiB;EA2BjBC,SAAA;EAGAC,MAAA;EANFC,IAAI,GAAGtC,OAAO,CAACuC,iBAAiB;EAChC,CAACnB,kBAAkB,IAAII,oBAAoB;EACpDgB,YACWJ,SAEmE,EACnEC,MAEoE;IALpE,KAAAD,SAAS,GAATA,SAAS;IAGT,KAAAC,MAAM,GAANA,MAAM;EAIjB;EACAI,MAAMA,CACJC,IAAgC;IAEhC,OAAO7C,IAAI,CAAC8C,SAAS,CAACD,IAAI,CAAC,GAAG,IAAI,CAACL,MAAM,CAACK,IAAI,CAACE,KAAK,CAAC,GAAG,IAAI,CAACR,SAAS,CAACM,IAAI,CAACG,KAAK,CAAC;EACpF;;AAGF;AAAAxB,OAAA,CAAAc,iBAAA,GAAAA,iBAAA;AACM,MAAOW,yBAAyB;EAKfC,SAAA;EAFZT,IAAI,GAAGtC,OAAO,CAACgD,yBAAyB;EACxC,CAAC5B,kBAAkB,IAAII,oBAAoB;EACpDgB,YAAqBO,SAAmF;IAAnF,KAAAA,SAAS,GAATA,SAAS;EAC9B", "ignoreList": []}