{"version": 3, "file": "rcMap.js", "names": ["Context", "_interopRequireWildcard", "require", "Duration", "_Function", "MutableHashMap", "_Pipeable", "coreEffect", "core", "circular", "fiberRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TypeId", "exports", "Symbol", "for", "variance", "_K", "identity", "_A", "_E", "RcMapImpl", "lookup", "context", "scope", "idleTimeToLive", "capacity", "state", "_tag", "map", "empty", "semaphore", "unsafeMakeSemaphore", "constructor", "pipe", "pipeArguments", "arguments", "make", "options", "withFiberRuntime", "fiber", "getFiberRef", "currentContext", "scopeTag", "self", "decode", "undefined", "Math", "max", "Number", "POSITIVE_INFINITY", "as", "addFinalizer", "suspend", "void", "forEachSequentialDiscard", "entry", "scopeClose", "exitVoid", "tap", "clear", "withPermits", "dual", "self_", "key", "uninterruptibleMask", "restore", "getImpl", "fnUntraced", "interrupt", "value", "refCount", "isFinite", "size", "fail", "ExceededCapacityException", "acquire", "finalizer", "deferred<PERSON><PERSON><PERSON>", "deferred", "scopeMake", "deferred<PERSON><PERSON>", "contextMap", "Map", "unsafeMap", "mapInputContext", "inputContext", "for<PERSON>ach", "unsafeMake", "exit", "flatMap", "deferredDone", "forkIn", "expiresAt", "release", "clockWith", "clock", "remove", "unsafeCurrentTimeMillis", "<PERSON><PERSON><PERSON><PERSON>", "interruptibleMask", "loop", "now", "remaining", "sleep", "millis", "ensuring", "sync", "keys", "impl", "succeed", "invalidate", "interruptFiber", "touch"], "sources": ["../../../src/internal/rcMap.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AAGA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AAGA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,IAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,QAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,YAAA,GAAAT,uBAAA,CAAAC,OAAA;AAAiD,SAAAD,wBAAAU,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAU,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEjD;AACO,MAAMkB,MAAM,GAAAC,OAAA,CAAAD,MAAA,gBAAiBE,MAAM,CAACC,GAAG,CAAC,cAAc,CAAiB;AAwB9E,MAAMC,QAAQ,GAAwC;EACpDC,EAAE,EAAEC,kBAAQ;EACZC,EAAE,EAAED,kBAAQ;EACZE,EAAE,EAAEF;CACL;AAED,MAAMG,SAAS;EAUFC,MAAA;EACAC,OAAA;EACAC,KAAA;EACAC,cAAA;EACAC,QAAA;EAbF,CAACd,MAAM;EAEhBe,KAAK,GAAmB;IACtBC,IAAI,EAAE,MAAM;IACZC,GAAG,eAAE1C,cAAc,CAAC2C,KAAK;GAC1B;EACQC,SAAS,gBAAGxC,QAAQ,CAACyC,mBAAmB,CAAC,CAAC,CAAC;EAEpDC,YACWX,MAA6C,EAC7CC,OAA+B,EAC/BC,KAAkB,EAClBC,cAA6C,EAC7CC,QAAgB;IAJhB,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IAEjB,IAAI,CAACd,MAAM,CAAC,GAAGI,QAAQ;EACzB;EAEAkB,IAAIA,CAAA;IACF,OAAO,IAAAC,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AACO,MAAMC,IAAI,GAWAC,OAIhB,IACChD,IAAI,CAACiD,gBAAgB,CAAgDC,KAAK,IAAI;EAC5E,MAAMjB,OAAO,GAAGiB,KAAK,CAACC,WAAW,CAACnD,IAAI,CAACoD,cAAc,CAAqC;EAC1F,MAAMlB,KAAK,GAAG1C,OAAO,CAACuB,GAAG,CAACkB,OAAO,EAAE/B,YAAY,CAACmD,QAAQ,CAAC;EACzD,MAAMC,IAAI,GAAG,IAAIvB,SAAS,CACxBiB,OAAO,CAAChB,MAAa,EACrBC,OAAO,EACPC,KAAK,EACLc,OAAO,CAACb,cAAc,GAAGxC,QAAQ,CAAC4D,MAAM,CAACP,OAAO,CAACb,cAAc,CAAC,GAAGqB,SAAS,EAC5EC,IAAI,CAACC,GAAG,CAACV,OAAO,CAACZ,QAAQ,IAAIuB,MAAM,CAACC,iBAAiB,EAAE,CAAC,CAAC,CAC1D;EACD,OAAO5D,IAAI,CAAC6D,EAAE,CACZ3B,KAAK,CAAC4B,YAAY,CAAC,MACjB9D,IAAI,CAAC+D,OAAO,CAAC,MAAK;IAChB,IAAIT,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;MAChC,OAAOtC,IAAI,CAACgE,IAAI;IAClB;IACA,MAAMzB,GAAG,GAAGe,IAAI,CAACjB,KAAK,CAACE,GAAG;IAC1Be,IAAI,CAACjB,KAAK,GAAG;MAAEC,IAAI,EAAE;IAAQ,CAAE;IAC/B,OAAOtC,IAAI,CAACiE,wBAAwB,CAClC1B,GAAG,EACH,CAAC,GAAG2B,KAAK,CAAC,KAAKlE,IAAI,CAACmE,UAAU,CAACD,KAAK,CAAChC,KAAK,EAAElC,IAAI,CAACoE,QAAQ,CAAC,CAC3D,CAACxB,IAAI,CACJ5C,IAAI,CAACqE,GAAG,CAAC,MAAK;MACZxE,cAAc,CAACyE,KAAK,CAAC/B,GAAG,CAAC;IAC3B,CAAC,CAAC,EACFe,IAAI,CAACb,SAAS,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAC9B;EACH,CAAC,CAAC,CACH,EACDjB,IAAI,CACL;AACH,CAAC,CAAC;AAEJ;AAAA/B,OAAA,CAAAwB,IAAA,GAAAA,IAAA;AACO,MAAMhC,GAAG,GAAAQ,OAAA,CAAAR,GAAA,gBAGZ,IAAAyD,cAAI,EAAC,CAAC,EAAE,CAAUC,KAA2B,EAAEC,GAAM,KAA+B;EACtF,MAAMpB,IAAI,GAAGmB,KAA2B;EACxC,OAAOzE,IAAI,CAAC2E,mBAAmB,CAAEC,OAAO,IAAKC,OAAO,CAACvB,IAAI,EAAEoB,GAAG,EAAEE,OAAc,CAAC,CAAC;AAClF,CAAC,CAAC;AAEF,MAAMC,OAAO,gBAAG7E,IAAI,CAAC8E,UAAU,CAAC,WAAmBxB,IAAwB,EAAEoB,GAAM,EAAEE,OAAuB;EAC1G,IAAItB,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;IAChC,OAAO,OAAOtC,IAAI,CAAC+E,SAAS;EAC9B;EACA,MAAM1C,KAAK,GAAGiB,IAAI,CAACjB,KAAK;EACxB,MAAM5B,CAAC,GAAGZ,cAAc,CAACkB,GAAG,CAACsB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;EAC5C,IAAIR,KAAwB;EAC5B,IAAIzD,CAAC,CAAC6B,IAAI,KAAK,MAAM,EAAE;IACrB4B,KAAK,GAAGzD,CAAC,CAACuE,KAAK;IACfd,KAAK,CAACe,QAAQ,EAAE;EAClB,CAAC,MAAM,IAAItB,MAAM,CAACuB,QAAQ,CAAC5B,IAAI,CAAClB,QAAQ,CAAC,IAAIvC,cAAc,CAACsF,IAAI,CAAC7B,IAAI,CAACjB,KAAK,CAACE,GAAG,CAAC,IAAIe,IAAI,CAAClB,QAAQ,EAAE;IACjG,OAAO,OAAOpC,IAAI,CAACoF,IAAI,CACrB,IAAIpF,IAAI,CAACqF,yBAAyB,CAAC,yCAAyC/B,IAAI,CAAClB,QAAQ,EAAE,CAAC,CAC5E;EACpB,CAAC,MAAM;IACL8B,KAAK,GAAG,OAAOZ,IAAI,CAACb,SAAS,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAACe,OAAO,CAAChC,IAAI,EAAEoB,GAAG,EAAEE,OAAO,CAAC,CAAC;EAC3E;EACA,MAAM1C,KAAK,GAAG,OAAOhC,YAAY,CAACmD,QAAQ;EAC1C,OAAOnB,KAAK,CAAC4B,YAAY,CAAC,MAAMI,KAAK,CAACqB,SAAS,CAAC;EAChD,OAAO,OAAOX,OAAO,CAAC5E,IAAI,CAACwF,aAAa,CAACtB,KAAK,CAACuB,QAAQ,CAAC,CAAC;AAC3D,CAAC,CAAC;AAEF,MAAMH,OAAO,gBAAGtF,IAAI,CAAC8E,UAAU,CAAC,WAAmBxB,IAAwB,EAAEoB,GAAM,EAAEE,OAAuB;EAC1G,MAAM1C,KAAK,GAAG,OAAOhC,YAAY,CAACwF,SAAS,EAAE;EAC7C,MAAMD,QAAQ,GAAG,OAAOzF,IAAI,CAAC2F,YAAY,EAAQ;EACjD,MAAML,OAAO,GAAGhC,IAAI,CAACtB,MAAM,CAAC0C,GAAG,CAAC;EAChC,MAAMkB,UAAU,GAAG,IAAIC,GAAG,CAACvC,IAAI,CAACrB,OAAO,CAAC6D,SAAS,CAAC;EAClD,OAAOlB,OAAO,CAAC5E,IAAI,CAAC+F,eAAe,CACjCT,OAAuB,EACtBU,YAAoC,IAAI;IACvCA,YAAY,CAACF,SAAS,CAACG,OAAO,CAAC,CAACjB,KAAK,EAAEN,GAAG,KAAI;MAC5CkB,UAAU,CAAC5E,GAAG,CAAC0D,GAAG,EAAEM,KAAK,CAAC;IAC5B,CAAC,CAAC;IACFY,UAAU,CAAC5E,GAAG,CAACd,YAAY,CAACmD,QAAQ,CAACqB,GAAG,EAAExC,KAAK,CAAC;IAChD,OAAO1C,OAAO,CAAC0G,UAAU,CAACN,UAAU,CAAC;EACvC,CAAC,CACF,CAAC,CAAChD,IAAI,CACL5C,IAAI,CAACmG,IAAI,EACTnG,IAAI,CAACoG,OAAO,CAAED,IAAI,IAAKnG,IAAI,CAACqG,YAAY,CAACZ,QAAQ,EAAEU,IAAI,CAAC,CAAC,EACzDlG,QAAQ,CAACqG,MAAM,CAACpE,KAAK,CAAC,CACvB;EACD,MAAMgC,KAAK,GAAsB;IAC/BuB,QAAQ;IACRvD,KAAK;IACLqD,SAAS,EAAE/B,SAAgB;IAC3BN,KAAK,EAAEM,SAAS;IAChB+C,SAAS,EAAE,CAAC;IACZtB,QAAQ,EAAE;GACX;EACCf,KAAa,CAACqB,SAAS,GAAGiB,OAAO,CAAClD,IAAI,EAAEoB,GAAG,EAAER,KAAK,CAAC;EACrD,IAAIZ,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,MAAM,EAAE;IAC9BzC,cAAc,CAACmB,GAAG,CAACsC,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,EAAER,KAAK,CAAC;EAChD;EACA,OAAOA,KAAK;AACd,CAAC,CAAC;AAEF,MAAMsC,OAAO,GAAGA,CAAUlD,IAAwB,EAAEoB,GAAM,EAAER,KAAwB,KAClFnE,UAAU,CAAC0G,SAAS,CAAEC,KAAK,IAAI;EAC7BxC,KAAK,CAACe,QAAQ,EAAE;EAChB,IAAIf,KAAK,CAACe,QAAQ,GAAG,CAAC,EAAE;IACtB,OAAOjF,IAAI,CAACgE,IAAI;EAClB,CAAC,MAAM,IACLV,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,IACzB,CAACzC,cAAc,CAACiB,GAAG,CAACwC,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC,IACxCpB,IAAI,CAACnB,cAAc,KAAKqB,SAAS,EACpC;IACA,IAAIF,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,MAAM,EAAE;MAC9BzC,cAAc,CAAC8G,MAAM,CAACrD,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;IAC5C;IACA,OAAO1E,IAAI,CAACmE,UAAU,CAACD,KAAK,CAAChC,KAAK,EAAElC,IAAI,CAACoE,QAAQ,CAAC;EACpD;EAEA,IAAI,CAACzE,QAAQ,CAACuF,QAAQ,CAAC5B,IAAI,CAACnB,cAAc,CAAC,EAAE;IAC3C,OAAOnC,IAAI,CAACgE,IAAI;EAClB;EAEAE,KAAK,CAACqC,SAAS,GAAGG,KAAK,CAACE,uBAAuB,EAAE,GAAGjH,QAAQ,CAACkH,QAAQ,CAACvD,IAAI,CAACnB,cAAc,CAAC;EAC1F,IAAI+B,KAAK,CAAChB,KAAK,EAAE,OAAOlD,IAAI,CAACgE,IAAI;EAEjC,OAAOhE,IAAI,CAAC8G,iBAAiB,CAAC,SAASC,IAAIA,CAACnC,OAAO;IACjD,MAAMoC,GAAG,GAAGN,KAAK,CAACE,uBAAuB,EAAE;IAC3C,MAAMK,SAAS,GAAG/C,KAAK,CAACqC,SAAS,GAAGS,GAAG;IACvC,IAAIC,SAAS,IAAI,CAAC,EAAE;MAClB,IAAI3D,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,IAAI4B,KAAK,CAACe,QAAQ,GAAG,CAAC,EAAE,OAAOjF,IAAI,CAACgE,IAAI;MACxEnE,cAAc,CAAC8G,MAAM,CAACrD,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;MAC1C,OAAOE,OAAO,CAAC5E,IAAI,CAACmE,UAAU,CAACD,KAAK,CAAChC,KAAK,EAAElC,IAAI,CAACoE,QAAQ,CAAC,CAAC;IAC7D;IACA,OAAOpE,IAAI,CAACoG,OAAO,CAACM,KAAK,CAACQ,KAAK,CAACvH,QAAQ,CAACwH,MAAM,CAACF,SAAS,CAAC,CAAC,EAAE,MAAMF,IAAI,CAACnC,OAAO,CAAC,CAAC;EACnF,CAAC,CAAC,CAAChC,IAAI,CACL1C,YAAY,CAACkH,QAAQ,CAACpH,IAAI,CAACqH,IAAI,CAAC,MAAK;IACnCnD,KAAK,CAAChB,KAAK,GAAGM,SAAS;EACzB,CAAC,CAAC,CAAC,EACHvD,QAAQ,CAACqG,MAAM,CAAChD,IAAI,CAACpB,KAAK,CAAC,EAC3BlC,IAAI,CAACqE,GAAG,CAAEnB,KAAK,IAAI;IACjBgB,KAAK,CAAChB,KAAK,GAAGA,KAAK;EACrB,CAAC,CAAC,EACFI,IAAI,CAACb,SAAS,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAC9B;AACH,CAAC,CAAC;AAEJ;AACO,MAAM+C,IAAI,GAAahE,IAA0B,IAAsB;EAC5E,MAAMiE,IAAI,GAAGjE,IAA0B;EACvC,OAAOtD,IAAI,CAAC+D,OAAO,CAAC,MAClBwD,IAAI,CAAClF,KAAK,CAACC,IAAI,KAAK,QAAQ,GAAGtC,IAAI,CAAC+E,SAAS,GAAG/E,IAAI,CAACwH,OAAO,CAAC3H,cAAc,CAACyH,IAAI,CAACC,IAAI,CAAClF,KAAK,CAACE,GAAG,CAAC,CAAC,CAClG;AACH,CAAC;AAED;AAAAhB,OAAA,CAAA+F,IAAA,GAAAA,IAAA;AACO,MAAMG,UAAU,GAAAlG,OAAA,CAAAkG,UAAA,gBAGnB,IAAAjD,cAAI,EACN,CAAC,eACDxE,IAAI,CAAC8E,UAAU,CAAC,WAAmBL,KAA2B,EAAEC,GAAM;EACpE,MAAMpB,IAAI,GAAGmB,KAA2B;EACxC,IAAInB,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;EAClC,MAAM7B,CAAC,GAAGZ,cAAc,CAACkB,GAAG,CAACuC,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;EACjD,IAAIjE,CAAC,CAAC6B,IAAI,KAAK,MAAM,EAAE;EACvB,MAAM4B,KAAK,GAAGzD,CAAC,CAACuE,KAAK;EACrBnF,cAAc,CAAC8G,MAAM,CAACrD,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;EAC1C,IAAIR,KAAK,CAACe,QAAQ,GAAG,CAAC,EAAE;EACxB,OAAOjF,IAAI,CAACmE,UAAU,CAACD,KAAK,CAAChC,KAAK,EAAElC,IAAI,CAACoE,QAAQ,CAAC;EAClD,IAAIF,KAAK,CAAChB,KAAK,EAAE,OAAOlD,IAAI,CAAC0H,cAAc,CAACxD,KAAK,CAAChB,KAAK,CAAC;AAC1D,CAAC,CAAC,CACH;AAED;AACO,MAAMyE,KAAK,GAAApG,OAAA,CAAAoG,KAAA,gBAGd,IAAAnD,cAAI,EACN,CAAC,EACD,CAAUC,KAA2B,EAAEC,GAAM,KAC3C3E,UAAU,CAAC0G,SAAS,CAAEC,KAAK,IAAI;EAC7B,MAAMpD,IAAI,GAAGmB,KAA2B;EACxC,IAAI,CAACnB,IAAI,CAACnB,cAAc,IAAImB,IAAI,CAACjB,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE,OAAOtC,IAAI,CAACgE,IAAI;EAC1E,MAAMvD,CAAC,GAAGZ,cAAc,CAACkB,GAAG,CAACuC,IAAI,CAACjB,KAAK,CAACE,GAAG,EAAEmC,GAAG,CAAC;EACjD,IAAIjE,CAAC,CAAC6B,IAAI,KAAK,MAAM,EAAE,OAAOtC,IAAI,CAACgE,IAAI;EACvCvD,CAAC,CAACuE,KAAK,CAACuB,SAAS,GAAGG,KAAK,CAACE,uBAAuB,EAAE,GAAGjH,QAAQ,CAACkH,QAAQ,CAACvD,IAAI,CAACnB,cAAc,CAAC;EAC5F,OAAOnC,IAAI,CAACgE,IAAI;AAClB,CAAC,CAAC,CACL", "ignoreList": []}