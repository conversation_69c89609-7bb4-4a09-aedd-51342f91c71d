{"version": 3, "file": "TPubSub.js", "names": ["internal", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TPubSubTypeId", "exports", "await<PERSON><PERSON><PERSON>down", "bounded", "capacity", "dropping", "isEmpty", "isFull", "shutdown", "isShutdown", "publish", "publishAll", "size", "sliding", "subscribe", "subscribeScoped", "unbounded"], "sources": ["../../src/TPubSub.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAKA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAqD,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAQrD;;;;AAIO,MAAMkB,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAkBtB,QAAQ,CAACsB,aAAa;AAsClE;;;;;;;;AAQO,MAAME,aAAa,GAAAD,OAAA,CAAAC,aAAA,GAA2CxB,QAAQ,CAACwB,aAAa;AAE3F;;;;;;;;AAQO,MAAMC,OAAO,GAAAF,OAAA,CAAAE,OAAA,GAA0DzB,QAAQ,CAACyB,OAAO;AAE9F;;;;;;AAMO,MAAMC,QAAQ,GAAAH,OAAA,CAAAG,QAAA,GAAoC1B,QAAQ,CAAC0B,QAAQ;AAE1E;;;;;;;AAOO,MAAMC,QAAQ,GAAAJ,OAAA,CAAAI,QAAA,GAA0D3B,QAAQ,CAAC2B,QAAQ;AAEhG;;;;;;AAMO,MAAMC,OAAO,GAAAL,OAAA,CAAAK,OAAA,GAA8C5B,QAAQ,CAAC4B,OAAO;AAElF;;;;;;;AAOO,MAAMC,MAAM,GAAAN,OAAA,CAAAM,MAAA,GAA8C7B,QAAQ,CAAC6B,MAAM;AAEhF;;;;;;;AAOO,MAAMC,QAAQ,GAAAP,OAAA,CAAAO,QAAA,GAA2C9B,QAAQ,CAAC8B,QAAQ;AAEjF;;;;;;AAMO,MAAMC,UAAU,GAAAR,OAAA,CAAAQ,UAAA,GAA8C/B,QAAQ,CAAC+B,UAAU;AAExF;;;;;;;AAOO,MAAMC,OAAO,GAAAT,OAAA,CAAAS,OAAA,GAiBhBhC,QAAQ,CAACgC,OAAO;AAEpB;;;;;;;AAOO,MAAMC,UAAU,GAAAV,OAAA,CAAAU,UAAA,GAiBnBjC,QAAQ,CAACiC,UAAU;AAEvB;;;;;;;;AAQO,MAAMC,IAAI,GAAAX,OAAA,CAAAW,IAAA,GAA6ClC,QAAQ,CAACkC,IAAI;AAE3E;;;;;;;;;AASO,MAAMC,OAAO,GAAAZ,OAAA,CAAAY,OAAA,GAA0DnC,QAAQ,CAACmC,OAAO;AAE9F;;;;;;;;;AASO,MAAMC,SAAS,GAAAb,OAAA,CAAAa,SAAA,GAAyDpC,QAAQ,CAACoC,SAAS;AAEjG;;;;;;;;AAQO,MAAMC,eAAe,GAAAd,OAAA,CAAAc,eAAA,GAC1BrC,QAAQ,CAACqC,eAAe;AAE1B;;;;;;AAMO,MAAMC,SAAS,GAAAf,OAAA,CAAAe,SAAA,GAAiCtC,QAAQ,CAACsC,SAAS", "ignoreList": []}