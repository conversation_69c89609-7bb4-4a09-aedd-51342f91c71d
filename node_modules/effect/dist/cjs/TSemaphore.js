"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withPermitsScoped = exports.withPermits = exports.withPermitScoped = exports.withPermit = exports.unsafeMake = exports.releaseN = exports.release = exports.make = exports.available = exports.acquireN = exports.acquire = exports.TSemaphoreTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/stm/tSemaphore.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category symbols
 */
const TSemaphoreTypeId = exports.TSemaphoreTypeId = internal.TSemaphoreTypeId;
/**
 * @since 2.0.0
 * @category mutations
 */
const acquire = exports.acquire = internal.acquire;
/**
 * @since 2.0.0
 * @category mutations
 */
const acquireN = exports.acquireN = internal.acquireN;
/**
 * @since 2.0.0
 * @category getters
 */
const available = exports.available = internal.available;
/**
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * @since 2.0.0
 * @category mutations
 */
const release = exports.release = internal.release;
/**
 * @since 2.0.0
 * @category mutations
 */
const releaseN = exports.releaseN = internal.releaseN;
/**
 * @since 2.0.0
 * @category mutations
 */
const withPermit = exports.withPermit = internal.withPermit;
/**
 * @since 2.0.0
 * @category mutations
 */
const withPermits = exports.withPermits = internal.withPermits;
/**
 * @since 2.0.0
 * @category mutations
 */
const withPermitScoped = exports.withPermitScoped = internal.withPermitScoped;
/**
 * @since 2.0.0
 * @category mutations
 */
const withPermitsScoped = exports.withPermitsScoped = internal.withPermitsScoped;
/**
 * @since 2.0.0
 * @category unsafe
 */
const unsafeMake = exports.unsafeMake = internal.unsafeMakeSemaphore;
//# sourceMappingURL=TSemaphore.js.map