{"version": 3, "file": "Utils.js", "names": ["_Function", "require", "_GlobalValue", "_errors", "_Predicate", "GenKindTypeId", "exports", "Symbol", "for", "isGenKind", "u", "isObject", "GenKindImpl", "value", "constructor", "_F", "identity", "_R", "_", "_O", "_E", "iterator", "SingleShotGen", "self", "called", "next", "a", "done", "return", "throw", "e", "makeGenKind", "kind", "adapter", "x", "arguments", "i", "length", "defaultIncHi", "defaultIncLo", "MUL_HI", "MUL_LO", "BIT_53", "BIT_27", "PCGRandom", "_state", "seedHi", "seedLo", "incHi", "incLo", "isNullable", "Math", "random", "Int32Array", "_next", "add64", "getState", "setState", "state", "integer", "max", "round", "number", "Number", "MAX_SAFE_INTEGER", "hi", "lo", "oldHi", "oldLo", "mul64", "xsHi", "xsLo", "xorshifted", "rot", "rot2", "out", "aHi", "aLo", "bHi", "bLo", "c1", "c0", "imul", "YieldWrapTypeId", "YieldWrap", "yieldWrapGet", "Error", "getBugErrorMessage", "structuralRegionState", "globalValue", "enabled", "tester", "undefined", "structuralRegion", "body", "current", "currentTester", "standard", "effect_internal_function", "forced", "isNotOptimizedAway", "stack", "includes", "internalCall", "genConstructor", "isGeneratorFunction"], "sources": ["../../src/Utils.ts"], "sourcesContent": [null], "mappings": ";;;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAPA;;;;AAUA;;;;;;;;;;;;AAaA;;;;AAIO,MAAMI,aAAa,GAAAC,OAAA,CAAAD,aAAA,gBAAkBE,MAAM,CAACC,GAAG,CAAC,oBAAoB,CAAC;AAkB5E;;;;AAIO,MAAMC,SAAS,GAAIC,CAAU,IAA4C,IAAAC,mBAAQ,EAACD,CAAC,CAAC,IAAIL,aAAa,IAAIK,CAAC;AAEjH;;;;AAAAJ,OAAA,CAAAG,SAAA,GAAAA,SAAA;AAIM,MAAOG,WAAW;EAKXC,KAAA;EAJXC;EACE;;;EAGSD,KAA0B;IAA1B,KAAAA,KAAK,GAALA,KAAK;EACb;EAEH;;;EAGA,IAAIE,EAAEA,CAAA;IACJ,OAAOC,kBAAQ;EACjB;EAEA;;;EAGA,IAAIC,EAAEA,CAAA;IACJ,OAAQC,CAAI,IAAKA,CAAC;EACpB;EAEA;;;EAGA,IAAIC,EAAEA,CAAA;IACJ,OAAQD,CAAQ,IAAQA,CAAC;EAC3B;EAEA;;;EAGA,IAAIE,EAAEA,CAAA;IACJ,OAAQF,CAAQ,IAAQA,CAAC;EAC3B;EAEA;;;EAGS,CAACb,aAAa,IAA0BA,aAAa;EAE9D;;;EAGA,CAACE,MAAM,CAACc,QAAQ,IAAC;IACf,OAAO,IAAIC,aAAa,CAA4B,IAAW,CAAC;EAClE;;AAGF;;;;AAAAhB,OAAA,CAAAM,WAAA,GAAAA,WAAA;AAIM,MAAOU,aAAa;EAGHC,IAAA;EAFbC,MAAM,GAAG,KAAK;EAEtBV,YAAqBS,IAAO;IAAP,KAAAA,IAAI,GAAJA,IAAI;EAAM;EAE/B;;;EAGAE,IAAIA,CAACC,CAAI;IACP,OAAO,IAAI,CAACF,MAAM,GACf;MACCX,KAAK,EAAEa,CAAC;MACRC,IAAI,EAAE;KACP,IACA,IAAI,CAACH,MAAM,GAAG,IAAI,EAChB;MACCX,KAAK,EAAE,IAAI,CAACU,IAAI;MAChBI,IAAI,EAAE;KACN,CAAC;EACT;EAEA;;;EAGAC,MAAMA,CAACF,CAAI;IACT,OAAQ;MACNb,KAAK,EAAEa,CAAC;MACRC,IAAI,EAAE;KACP;EACH;EAEA;;;EAGAE,KAAKA,CAACC,CAAU;IACd,MAAMA,CAAC;EACT;EAEA;;;EAGA,CAACvB,MAAM,CAACc,QAAQ,IAAC;IACf,OAAO,IAAIC,aAAa,CAAO,IAAI,CAACC,IAAI,CAAC;EAC3C;;AAGF;;;;AAAAjB,OAAA,CAAAgB,aAAA,GAAAA,aAAA;AAIO,MAAMS,WAAW,GACtBC,IAAyB,IACE,IAAIpB,WAAW,CAACoB,IAAI,CAAC;AA4TlD;;;;AAAA1B,OAAA,CAAAyB,WAAA,GAAAA,WAAA;AAIO,MAAME,OAAO,GAA2CA,CAAA,KAAO;EACpE,IAAIC,CAAC,GAAGC,SAAS,CAAC,CAAC,CAAC;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACzCF,CAAC,GAAGC,SAAS,CAACC,CAAC,CAAC,CAACF,CAAC,CAAC;EACrB;EACA,OAAO,IAAItB,WAAW,CAACsB,CAAC,CAAQ;AAClC,CAAE;AAAA5B,OAAA,CAAA2B,OAAA,GAAAA,OAAA;AAEF,MAAMK,YAAY,GAAG,UAAU;AAC/B,MAAMC,YAAY,GAAG,UAAU;AAC/B,MAAMC,MAAM,GAAG,UAAU,KAAK,CAAC;AAC/B,MAAMC,MAAM,GAAG,UAAU,KAAK,CAAC;AAC/B,MAAMC,MAAM,GAAG,kBAAkB;AACjC,MAAMC,MAAM,GAAG,WAAW;AAc1B;;;;;;;;AAQM,MAAOC,SAAS;EACZC,MAAM;EAoCd/B,YACEgC,MAAuB,EACvBC,MAAuB,EACvBC,KAAsB,EACtBC,KAAsB;IAEtB,IAAI,IAAAC,qBAAU,EAACH,MAAM,CAAC,IAAI,IAAAG,qBAAU,EAACJ,MAAM,CAAC,EAAE;MAC5CC,MAAM,GAAII,IAAI,CAACC,MAAM,EAAE,GAAG,UAAU,KAAM,CAAC;MAC3CN,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM,IAAI,IAAAI,qBAAU,EAACH,MAAM,CAAC,EAAE;MAC7BA,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAG,CAAC;IACZ;IACA,IAAI,IAAAI,qBAAU,EAACD,KAAK,CAAC,IAAI,IAAAC,qBAAU,EAACF,KAAK,CAAC,EAAE;MAC1CC,KAAK,GAAG,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGN,YAAY;MACnDS,KAAK,GAAG,IAAI,CAACH,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGP,YAAY;IACrD,CAAC,MAAM,IAAI,IAAAY,qBAAU,EAACD,KAAK,CAAC,EAAE;MAC5BA,KAAK,GAAYD,KAAK;MACtBA,KAAK,GAAG,CAAC;IACX;IAEA,IAAI,CAACH,MAAM,GAAG,IAAIQ,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAYL,KAAM,KAAK,CAAC,EAAE,CAAC,CAACC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACtF,IAAI,CAACK,KAAK,EAAE;IACZC,KAAK,CACH,IAAI,CAACV,MAAM,EACX,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EACf,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EACLC,MAAO,KAAK,CAAC,EACbC,MAAO,KAAK,CAAC,CACxB;IACD,IAAI,CAACO,KAAK,EAAE;IACZ,OAAO,IAAI;EACb;EAEA;;;;;;;EAOAE,QAAQA,CAAA;IACN,OAAO,CAAC,IAAI,CAACX,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,CAAC;EAC7E;EAEA;;;;;EAKAY,QAAQA,CAACC,KAAqB;IAC5B,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,GAAGa,KAAK,CAAC,CAAC,CAAC;IACzB,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,GAAGa,KAAK,CAAC,CAAC,CAAC;IACzB,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,GAAGa,KAAK,CAAC,CAAC,CAAC;IACzB,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,GAAGa,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;EAC/B;EAEA;;;;;;EAMAC,OAAOA,CAACC,GAAW;IACjB,OAAOT,IAAI,CAACU,KAAK,CAAC,IAAI,CAACC,MAAM,EAAE,GAAGC,MAAM,CAACC,gBAAgB,CAAC,GAAGJ,GAAG;EAClE;EAEA;;;;;;;EAOAE,MAAMA,CAAA;IACJ,MAAMG,EAAE,GAAG,CAAC,IAAI,CAACX,KAAK,EAAE,GAAG,UAAU,IAAI,GAAG;IAC5C,MAAMY,EAAE,GAAG,CAAC,IAAI,CAACZ,KAAK,EAAE,GAAG,UAAU,IAAI,GAAG;IAC5C,OAAO,CAACW,EAAE,GAAGtB,MAAM,GAAGuB,EAAE,IAAIxB,MAAM;EACpC;EAEA;EACQY,KAAKA,CAAA;IACX;IACA,MAAMa,KAAK,GAAG,IAAI,CAACtB,MAAM,CAAC,CAAC,CAAE,KAAK,CAAC;IACnC,MAAMuB,KAAK,GAAG,IAAI,CAACvB,MAAM,CAAC,CAAC,CAAE,KAAK,CAAC;IAEnC;IACAwB,KAAK,CAAC,IAAI,CAACxB,MAAM,EAAEsB,KAAK,EAAEC,KAAK,EAAE5B,MAAM,EAAEC,MAAM,CAAC;IAChDc,KAAK,CAAC,IAAI,CAACV,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,CAAE,CAAC;IAEtF;IACA,IAAIyB,IAAI,GAAGH,KAAK,KAAK,EAAE;IACvB,IAAII,IAAI,GAAG,CAAEH,KAAK,KAAK,EAAE,GAAKD,KAAK,IAAI,EAAG,MAAM,CAAC;IACjDG,IAAI,GAAG,CAACA,IAAI,GAAGH,KAAK,MAAM,CAAC;IAC3BI,IAAI,GAAG,CAACA,IAAI,GAAGH,KAAK,MAAM,CAAC;IAC3B,MAAMI,UAAU,GAAG,CAAED,IAAI,KAAK,EAAE,GAAKD,IAAI,IAAI,CAAE,MAAM,CAAC;IACtD;IACA;IACA,MAAMG,GAAG,GAAGN,KAAK,KAAK,EAAE;IACxB,MAAMO,IAAI,GAAG,CAAE,CAACD,GAAG,KAAK,CAAC,GAAI,EAAE,MAAM,CAAC;IACtC,OAAO,CAAED,UAAU,KAAKC,GAAG,GAAKD,UAAU,IAAIE,IAAK,MAAM,CAAC;EAC5D;;;AAGF,SAASL,KAAKA,CACZM,GAAe,EACfC,GAAW,EACXC,GAAW,EACXC,GAAW,EACXC,GAAW;EAEX,IAAIC,EAAE,GAAI,CAACH,GAAG,KAAK,EAAE,KAAKE,GAAG,GAAG,MAAM,CAAC,KAAM,CAAC;EAC9C,IAAIE,EAAE,GAAI,CAACJ,GAAG,GAAG,MAAM,KAAKE,GAAG,KAAK,EAAE,CAAC,KAAM,CAAC;EAE9C,IAAIb,EAAE,GAAI,CAACW,GAAG,GAAG,MAAM,KAAKE,GAAG,GAAG,MAAM,CAAC,KAAM,CAAC;EAChD,IAAId,EAAE,GAAI,CAACY,GAAG,KAAK,EAAE,KAAKE,GAAG,KAAK,EAAE,CAAC,IAAI,CAACE,EAAE,KAAK,EAAE,KAAKD,EAAE,KAAK,EAAE,CAAC,CAAC,KAAM,CAAC;EAE1EC,EAAE,GAAIA,EAAE,IAAI,EAAE,KAAM,CAAC;EACrBf,EAAE,GAAIA,EAAE,GAAGe,EAAE,KAAM,CAAC;EACpB,IAAKf,EAAE,KAAK,CAAC,GAAKe,EAAE,KAAK,CAAE,EAAE;IAC3BhB,EAAE,GAAIA,EAAE,GAAG,CAAC,KAAM,CAAC;EACrB;EAEAe,EAAE,GAAIA,EAAE,IAAI,EAAE,KAAM,CAAC;EACrBd,EAAE,GAAIA,EAAE,GAAGc,EAAE,KAAM,CAAC;EACpB,IAAKd,EAAE,KAAK,CAAC,GAAKc,EAAE,KAAK,CAAE,EAAE;IAC3Bf,EAAE,GAAIA,EAAE,GAAG,CAAC,KAAM,CAAC;EACrB;EAEAA,EAAE,GAAIA,EAAE,GAAGd,IAAI,CAAC+B,IAAI,CAACL,GAAG,EAAEC,GAAG,CAAC,KAAM,CAAC;EACrCb,EAAE,GAAIA,EAAE,GAAGd,IAAI,CAAC+B,IAAI,CAACN,GAAG,EAAEG,GAAG,CAAC,KAAM,CAAC;EAErCJ,GAAG,CAAC,CAAC,CAAC,GAAGV,EAAE;EACXU,GAAG,CAAC,CAAC,CAAC,GAAGT,EAAE;AACb;AAEA;AACA,SAASX,KAAKA,CACZoB,GAAe,EACfC,GAAW,EACXC,GAAW,EACXC,GAAW,EACXC,GAAW;EAEX,IAAId,EAAE,GAAIW,GAAG,GAAGE,GAAG,KAAM,CAAC;EAC1B,MAAMZ,EAAE,GAAIW,GAAG,GAAGE,GAAG,KAAM,CAAC;EAC5B,IAAKb,EAAE,KAAK,CAAC,GAAKW,GAAG,KAAK,CAAE,EAAE;IAC5BZ,EAAE,GAAIA,EAAE,GAAG,CAAC,GAAI,CAAC;EACnB;EACAU,GAAG,CAAC,CAAC,CAAC,GAAGV,EAAE;EACXU,GAAG,CAAC,CAAC,CAAC,GAAGT,EAAE;AACb;AAEA;;;AAGO,MAAMiB,eAAe,GAAA7E,OAAA,CAAA6E,eAAA,gBAAkB5E,MAAM,CAACC,GAAG,CAAC,wBAAwB,CAAC;AAElF;;;AAGM,MAAO4E,SAAS;EACpB;;;EAGS,CAAAvE,KAAM;EACfC,YAAYD,KAAQ;IAClB,IAAI,CAAC,CAAAA,KAAM,GAAGA,KAAK;EACrB;EACA;;;EAGA,CAACsE,eAAe,IAAC;IACf,OAAO,IAAI,CAAC,CAAAtE,KAAM;EACpB;;AAGF;;;AAAAP,OAAA,CAAA8E,SAAA,GAAAA,SAAA;AAGM,SAAUC,YAAYA,CAAI9D,IAAkB;EAChD,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI4D,eAAe,IAAI5D,IAAI,EAAE;IACxE,OAAOA,IAAI,CAAC4D,eAAe,CAAC,EAAE;EAChC;EACA,MAAM,IAAIG,KAAK,CAAC,IAAAC,0BAAkB,EAAC,cAAc,CAAC,CAAC;AACrD;AAEA;;;;;;;AAOO,MAAMC,qBAAqB,GAAAlF,OAAA,CAAAkF,qBAAA,gBAAG,IAAAC,wBAAW,EAC9C,iCAAiC,EACjC,OAAwF;EACtFC,OAAO,EAAE,KAAK;EACdC,MAAM,EAAEC;CACT,CAAC,CACH;AAED;;;;;;;AAOO,MAAMC,gBAAgB,GAAGA,CAAIC,IAAa,EAAEH,MAA4C,KAAO;EACpG,MAAMI,OAAO,GAAGP,qBAAqB,CAACE,OAAO;EAC7C,MAAMM,aAAa,GAAGR,qBAAqB,CAACG,MAAM;EAClDH,qBAAqB,CAACE,OAAO,GAAG,IAAI;EACpC,IAAIC,MAAM,EAAE;IACVH,qBAAqB,CAACG,MAAM,GAAGA,MAAM;EACvC;EACA,IAAI;IACF,OAAOG,IAAI,EAAE;EACf,CAAC,SAAS;IACRN,qBAAqB,CAACE,OAAO,GAAGK,OAAO;IACvCP,qBAAqB,CAACG,MAAM,GAAGK,aAAa;EAC9C;AACF,CAAC;AAAA1F,OAAA,CAAAuF,gBAAA,GAAAA,gBAAA;AAED,MAAMI,QAAQ,GAAG;EACfC,wBAAwB,EAAMJ,IAAa,IAAI;IAC7C,OAAOA,IAAI,EAAE;EACf;CACD;AAED,MAAMK,MAAM,GAAG;EACbD,wBAAwB,EAAMJ,IAAa,IAAI;IAC7C,IAAI;MACF,OAAOA,IAAI,EAAE;IACf,CAAC,SAAS;MACR;IAAA;EAEJ;CACD;AAED,MAAMM,kBAAkB,GACtB,aAAAH,QAAQ,CAACC,wBAAwB,CAAC,MAAM,IAAIZ,KAAK,EAAE,CAACe,KAAK,CAAC,EAAEC,QAAQ,CAAC,0BAA0B,CAAC,KAAK,IAAI;AAE3G;;;;;AAKO,MAAMC,YAAY,GAAAjG,OAAA,CAAAiG,YAAA,GAAGH,kBAAkB,GAAGH,QAAQ,CAACC,wBAAwB,GAAGC,MAAM,CAACD,wBAAwB;AAEpH,MAAMM,cAAc,GAAI,aAAS,CAAI,CAAC,CAAE1F,WAAW;AAEnD;;;AAGO,MAAM2F,mBAAmB,GAAI/F,CAAU,IAC5C,IAAAC,mBAAQ,EAACD,CAAC,CAAC,IAAIA,CAAC,CAACI,WAAW,KAAK0F,cAAc;AAAAlG,OAAA,CAAAmG,mBAAA,GAAAA,mBAAA", "ignoreList": []}