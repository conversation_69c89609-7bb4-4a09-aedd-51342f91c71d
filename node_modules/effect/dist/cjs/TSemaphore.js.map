{"version": 3, "file": "TSemaphore.js", "names": ["internal", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TSemaphoreTypeId", "exports", "acquire", "acquireN", "available", "make", "release", "releaseN", "with<PERSON><PERSON><PERSON>", "withPermits", "withPermitScoped", "withPermitsScoped", "unsafeMake", "unsafeMakeSemaphore"], "sources": ["../../src/TSemaphore.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAKA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAwD,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AALxD;;;;AAUA;;;;AAIO,MAAMkB,gBAAgB,GAAAC,OAAA,CAAAD,gBAAA,GAAkBtB,QAAQ,CAACsB,gBAAgB;AAmCxE;;;;AAIO,MAAME,OAAO,GAAAD,OAAA,CAAAC,OAAA,GAAwCxB,QAAQ,CAACwB,OAAO;AAE5E;;;;AAIO,MAAMC,QAAQ,GAAAF,OAAA,CAAAE,QAAA,GAWjBzB,QAAQ,CAACyB,QAAQ;AAErB;;;;AAIO,MAAMC,SAAS,GAAAH,OAAA,CAAAG,SAAA,GAA0C1B,QAAQ,CAAC0B,SAAS;AAElF;;;;AAIO,MAAMC,IAAI,GAAAJ,OAAA,CAAAI,IAAA,GAA6C3B,QAAQ,CAAC2B,IAAI;AAE3E;;;;AAIO,MAAMC,OAAO,GAAAL,OAAA,CAAAK,OAAA,GAAwC5B,QAAQ,CAAC4B,OAAO;AAE5E;;;;AAIO,MAAMC,QAAQ,GAAAN,OAAA,CAAAM,QAAA,GAWjB7B,QAAQ,CAAC6B,QAAQ;AAErB;;;;AAIO,MAAMC,UAAU,GAAAP,OAAA,CAAAO,UAAA,GAWnB9B,QAAQ,CAAC8B,UAAU;AAEvB;;;;AAIO,MAAMC,WAAW,GAAAR,OAAA,CAAAQ,WAAA,GAWpB/B,QAAQ,CAAC+B,WAAW;AAExB;;;;AAIO,MAAMC,gBAAgB,GAAAT,OAAA,CAAAS,gBAAA,GAAkEhC,QAAQ,CAACgC,gBAAgB;AAExH;;;;AAIO,MAAMC,iBAAiB,GAAAV,OAAA,CAAAU,iBAAA,GAW1BjC,QAAQ,CAACiC,iBAAiB;AAE9B;;;;AAIO,MAAMC,UAAU,GAAAX,OAAA,CAAAW,UAAA,GAAoClC,QAAQ,CAACmC,mBAAmB", "ignoreList": []}