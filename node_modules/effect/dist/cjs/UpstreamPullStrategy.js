"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.match = exports.isUpstreamPullStrategy = exports.isPullAfterNext = exports.isPullAfterAllEnqueued = exports.UpstreamPullStrategyTypeId = exports.PullAfterNext = exports.PullAfterAllEnqueued = void 0;
var internal = _interopRequireWildcard(require("./internal/channel/upstreamPullStrategy.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category symbols
 */
const UpstreamPullStrategyTypeId = exports.UpstreamPullStrategyTypeId = internal.UpstreamPullStrategyTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const PullAfterNext = exports.PullAfterNext = internal.PullAfterNext;
/**
 * @since 2.0.0
 * @category constructors
 */
const PullAfterAllEnqueued = exports.PullAfterAllEnqueued = internal.PullAfterAllEnqueued;
/**
 * Returns `true` if the specified value is an `UpstreamPullStrategy`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isUpstreamPullStrategy = exports.isUpstreamPullStrategy = internal.isUpstreamPullStrategy;
/**
 * Returns `true` if the specified `UpstreamPullStrategy` is a `PullAfterNext`,
 * `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isPullAfterNext = exports.isPullAfterNext = internal.isPullAfterNext;
/**
 * Returns `true` if the specified `UpstreamPullStrategy` is a
 * `PullAfterAllEnqueued`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isPullAfterAllEnqueued = exports.isPullAfterAllEnqueued = internal.isPullAfterAllEnqueued;
/**
 * Folds an `UpstreamPullStrategy<A>` into a value of type `Z`.
 *
 * @since 2.0.0
 * @category folding
 */
const match = exports.match = internal.match;
//# sourceMappingURL=UpstreamPullStrategy.js.map