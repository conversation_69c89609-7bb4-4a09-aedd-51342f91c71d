{"version": 3, "file": "UpstreamPullRequest.js", "names": ["internal", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "UpstreamPullRequestTypeId", "exports", "Pulled", "NoUpstream", "isUpstreamPullRequest", "isPulled", "isNoUpstream", "match"], "sources": ["../../src/UpstreamPullRequest.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAqE,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAHrE;;;;AAMA;;;;AAIO,MAAMkB,yBAAyB,GAAAC,OAAA,CAAAD,yBAAA,GAAkBtB,QAAQ,CAACsB,yBAAyB;AA+C1F;;;;AAIO,MAAME,MAAM,GAAAD,OAAA,CAAAC,MAAA,GAA4CxB,QAAQ,CAACwB,MAAM;AAE9E;;;;AAIO,MAAMC,UAAU,GAAAF,OAAA,CAAAE,UAAA,GAAkEzB,QAAQ,CAACyB,UAAU;AAE5G;;;;;;;AAOO,MAAMC,qBAAqB,GAAAH,OAAA,CAAAG,qBAAA,GAAsD1B,QAAQ,CAAC0B,qBAAqB;AAEtH;;;;;;;AAOO,MAAMC,QAAQ,GAAAJ,OAAA,CAAAI,QAAA,GAA2D3B,QAAQ,CAAC2B,QAAQ;AAEjG;;;;;;;AAOO,MAAMC,YAAY,GAAAL,OAAA,CAAAK,YAAA,GAA4D5B,QAAQ,CAAC4B,YAAY;AAE1G;;;;;;AAMO,MAAMC,KAAK,GAAAN,OAAA,CAAAM,KAAA,GA0Bd7B,QAAQ,CAAC6B,KAAK", "ignoreList": []}