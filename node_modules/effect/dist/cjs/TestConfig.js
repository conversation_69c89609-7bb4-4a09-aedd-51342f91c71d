"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.make = exports.TestConfig = void 0;
var Context = _interopRequireWildcard(require("./Context.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 */
const TestConfig = exports.TestConfig = /*#__PURE__*/Context.GenericTag("effect/TestConfig");
/**
 * @since 2.0.0
 */
const make = params => params;
exports.make = make;
//# sourceMappingURL=TestConfig.js.map