"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.updateSomeAndGet = exports.updateSome = exports.updateAndGet = exports.update = exports.setAndGet = exports.set = exports.modifySome = exports.modify = exports.make = exports.getAndUpdateSome = exports.getAndUpdate = exports.getAndSet = exports.get = exports.TRefTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/stm/tRef.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category symbols
 */
const TRefTypeId = exports.TRefTypeId = internal.TRefTypeId;
/**
 * @since 2.0.0
 * @category mutations
 */
const get = exports.get = internal.get;
/**
 * @since 2.0.0
 * @category mutations
 */
const getAndSet = exports.getAndSet = internal.getAndSet;
/**
 * @since 2.0.0
 * @category mutations
 */
const getAndUpdate = exports.getAndUpdate = internal.getAndUpdate;
/**
 * @since 2.0.0
 * @category mutations
 */
const getAndUpdateSome = exports.getAndUpdateSome = internal.getAndUpdateSome;
/**
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * @since 2.0.0
 * @category mutations
 */
const modify = exports.modify = internal.modify;
/**
 * @since 2.0.0
 * @category mutations
 */
const modifySome = exports.modifySome = internal.modifySome;
/**
 * @since 2.0.0
 * @category mutations
 */
const set = exports.set = internal.set;
/**
 * @since 2.0.0
 * @category mutations
 */
const setAndGet = exports.setAndGet = internal.setAndGet;
/**
 * @since 2.0.0
 * @category mutations
 */
const update = exports.update = internal.update;
/**
 * @since 2.0.0
 * @category mutations
 */
const updateAndGet = exports.updateAndGet = internal.updateAndGet;
/**
 * @since 2.0.0
 * @category mutations
 */
const updateSome = exports.updateSome = internal.updateSome;
/**
 * @since 2.0.0
 * @category mutations
 */
const updateSomeAndGet = exports.updateSomeAndGet = internal.updateSomeAndGet;
//# sourceMappingURL=TRef.js.map