"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.match = exports.isUpstreamPullRequest = exports.isPulled = exports.isNoUpstream = exports.UpstreamPullRequestTypeId = exports.Pulled = exports.NoUpstream = void 0;
var internal = _interopRequireWildcard(require("./internal/channel/upstreamPullRequest.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category symbols
 */
const UpstreamPullRequestTypeId = exports.UpstreamPullRequestTypeId = internal.UpstreamPullRequestTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const Pulled = exports.Pulled = internal.Pulled;
/**
 * @since 2.0.0
 * @category constructors
 */
const NoUpstream = exports.NoUpstream = internal.NoUpstream;
/**
 * Returns `true` if the specified value is an `UpstreamPullRequest`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isUpstreamPullRequest = exports.isUpstreamPullRequest = internal.isUpstreamPullRequest;
/**
 * Returns `true` if the specified `UpstreamPullRequest` is a `Pulled`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isPulled = exports.isPulled = internal.isPulled;
/**
 * Returns `true` if the specified `UpstreamPullRequest` is a `NoUpstream`,
 * `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isNoUpstream = exports.isNoUpstream = internal.isNoUpstream;
/**
 * Folds an `UpstreamPullRequest<A>` into a value of type `Z`.
 *
 * @since 2.0.0
 * @category folding
 */
const match = exports.match = internal.match;
//# sourceMappingURL=UpstreamPullRequest.js.map